import { useState, useCallback, useRef } from 'react';
import webSearchService from '../services/webSearchService';

const useWebSearchStreaming = () => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingResponse, setStreamingResponse] = useState('');
  const [streamingError, setStreamingError] = useState(null);
  const [showSignupModal, setShowSignupModal] = useState(false);
  const [signupModalMessage, setSignupModalMessage] = useState('');
  const abortControllerRef = useRef(null);

  const resetStreaming = useCallback(() => {
    setStreamingResponse('');
    setStreamingError(null);
    setShowSignupModal(false);
    setSignupModalMessage('');
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  const streamWebSearch = useCallback(async (query, options = {}) => {
    if (isStreaming) {
      console.warn('Already streaming a web search');
      return null;
    }

    setIsStreaming(true);
    setStreamingResponse('');
    setStreamingError(null);
    setShowSignupModal(false);
    setSignupModalMessage('');

    // Create abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      const streamCallbacks = {
        onStart: (data) => {
          // Web search started
        },
        onChunk: (content, fullResponse) => {
          setStreamingResponse(fullResponse);
        },
        onComplete: (fullResponse, data) => {
          setStreamingResponse(fullResponse);
          return fullResponse;
        },
        onError: (error) => {
          const handledError = webSearchService.handleStreamingError(error);

          // Check if this is a signup-required error
          if (handledError.isSignupRequired) {
            setSignupModalMessage(handledError.message);
            setShowSignupModal(true);
          } else {
            setStreamingError(handledError.message);
          }

          console.error('Web search streaming error:', handledError);
        }
      };

      const result = await webSearchService.streamWebSearch(query, {
        ...options,
        ...streamCallbacks,
        signal: abortControllerRef.current.signal
      });

      return result;
    } catch (error) {
      if (error.name !== 'AbortError') {
        const handledError = webSearchService.handleStreamingError(error);

        // Check if this is a signup-required error
        if (handledError.isSignupRequired) {
          setSignupModalMessage(handledError.message);
          setShowSignupModal(true);
        } else {
          setStreamingError(handledError.message);
        }

        console.error('Web search streaming error:', handledError);
      }
      return null;
    } finally {
      setIsStreaming(false);
      abortControllerRef.current = null;
    }
  }, [isStreaming]);

  const streamWebSearchWithFile = useCallback(async (query, file, options = {}) => {
    if (isStreaming) {
      console.warn('Already streaming a web search with file');
      return null;
    }

    setIsStreaming(true);
    setStreamingResponse('');
    setStreamingError(null);
    setShowSignupModal(false);
    setSignupModalMessage('');

    // Create abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      const streamCallbacks = {
        onStart: (data) => {
          // Web search with file started
        },
        onChunk: (content, fullResponse) => {
          setStreamingResponse(fullResponse);
        },
        onComplete: (fullResponse, data) => {
          setStreamingResponse(fullResponse);
          return fullResponse;
        },
        onError: (error) => {
          const handledError = webSearchService.handleStreamingError(error);

          // Check if this is a signup-required error
          if (handledError.isSignupRequired) {
            setSignupModalMessage(handledError.message);
            setShowSignupModal(true);
          } else {
            setStreamingError(handledError.message);
          }

          console.error('Web search with file streaming error:', handledError);
        }
      };

      const result = await webSearchService.streamWebSearchWithFile(query, file, {
        ...options,
        ...streamCallbacks,
        signal: abortControllerRef.current.signal
      });

      return result;
    } catch (error) {
      if (error.name !== 'AbortError') {
        const handledError = webSearchService.handleStreamingError(error);

        // Check if this is a signup-required error
        if (handledError.isSignupRequired) {
          setSignupModalMessage(handledError.message);
          setShowSignupModal(true);
        } else {
          setStreamingError(handledError.message);
        }

        console.error('Web search with file streaming error:', handledError);
      }
      return null;
    } finally {
      setIsStreaming(false);
      abortControllerRef.current = null;
    }
  }, [isStreaming]);

  const abortWebSearch = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsStreaming(false);
  }, []);

  const closeSignupModal = useCallback(() => {
    setShowSignupModal(false);
    setSignupModalMessage('');
  }, []);

  return {
    isStreaming,
    streamingResponse,
    streamingError,
    showSignupModal,
    signupModalMessage,
    streamWebSearch,
    streamWebSearchWithFile,
    abortWebSearch,
    resetStreaming,
    closeSignupModal
  };
};

export default useWebSearchStreaming;
