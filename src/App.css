.App {
  min-height: 100vh;
  background-color: var(--primary-bg);
}

/* Smooth scrolling for anchor links and hide scrollbar globally */
html {
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
}

/* Hide scrollbar globally for all browsers */
/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: transparent;
}

/* Internet Explorer and Edge Legacy */
body {
  -ms-overflow-style: none;
}

/* Loading Container Styles */
.loading-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1a1a1a;
  color: #ffffff;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #3a3a3a;
  border-top: 4px solid #fcd469;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner p {
  color: #b0b0b0;
  font-size: 16px;
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile viewport fixes */
@media (max-width: 768px) {
  html, body {
    overflow-x: hidden;
    position: relative;
    height: 100%;
  }

  /* Prevent zoom on input focus for iOS */
  input, textarea, select {
    font-size: 16px !important;
  }

  /* Fix for mobile Safari viewport issues */
  .App {
    min-height: 100vh;
    min-height: 100dvh;
  }
}
