import React from 'react';
import { useNavigate } from 'react-router-dom';
import SEO from '../components/common/SEO/SEO';
import './CareersPage.css';

const CareersPage = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate('/');
  };



  return (
    <div className="careers-page">
      <SEO
        title="Careers - Join the AI Revolution"
        description="Join the infini ai team and help build the future of AI technology. Work on cutting-edge ChatGPT alternatives and Claude alternatives. Explore career opportunities in AI development."
        keywords="AI careers, AI jobs, ChatGPT careers, Claude careers, AI development jobs, machine learning careers, AI startup jobs, tech careers"
        canonical="/careers"
      />
      {/* Header */}
      <div className="careers-page__header">
        <div className="careers-page__header-content">
          <div className="careers-page__container">
            <button
              onClick={handleGoBack}
              className="careers-page__back-btn"
              aria-label="Go back to home"
            >
              ← Back to Home
            </button>
          </div>
          <h1 className="careers-page__title">Careers at the infini ai</h1>
          <p className="careers-page__subtitle">
            Join us in shaping the future of AI-powered conversations
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="careers-page__content">
        <div className="careers-page__container">
          
          {/* No Positions Section */}
          <div className="careers-page__no-positions">
            <div className="careers-page__icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"/>
                <path d="M12 15l-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"/>
                <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"/>
                <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"/>
              </svg>
            </div>
            <h2 className="careers-page__no-positions-title">
              No Open Positions Currently
            </h2>
            <p className="careers-page__no-positions-description">
              We're not actively hiring at the moment, but we're always interested in connecting 
              with talented individuals who are passionate about AI and technology.
            </p>
          </div>

          {/* Future Opportunities */}
          <div className="careers-page__future">
            <h3 className="careers-page__future-title">Stay Connected</h3>
            <p className="careers-page__future-description">
              While we don't have any open positions right now, we're constantly growing and 
              evolving. We encourage you to check back periodically or reach out to us directly 
              if you're interested in future opportunities.
            </p>
            
            <div className="careers-page__contact-info">
              <h4>Interested in joining our team?</h4>
              <p>
                Feel free to reach out to us directly. We'd love to hear
                from you and learn about your background and interests.
              </p>
              <p>
                Write to us at: <a href="mailto:<EMAIL>" className="careers-page__email-link"><EMAIL></a>
              </p>
            </div>
          </div>

          {/* Company Values */}
          <div className="careers-page__values">
            <h3 className="careers-page__values-title">What We Value</h3>
            <div className="careers-page__values-grid">
              <div className="careers-page__value-item">
                <div className="careers-page__value-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"/>
                    <path d="M12 15l-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"/>
                    <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"/>
                    <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"/>
                  </svg>
                </div>
                <h4>Innovation</h4>
                <p>We're constantly pushing the boundaries of what's possible with AI technology.</p>
              </div>
              <div className="careers-page__value-item">
                <div className="careers-page__value-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                  </svg>
                </div>
                <h4>Privacy & Security</h4>
                <p>We prioritize user privacy and data security in everything we build.</p>
              </div>
              <div className="careers-page__value-item">
                <div className="careers-page__value-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26"/>
                  </svg>
                </div>
                <h4>User Experience</h4>
                <p>We believe in creating intuitive and accessible experiences for all users.</p>
              </div>
              <div className="careers-page__value-item">
                <div className="careers-page__value-icon">🤝</div>
                <h4>Collaboration</h4>
                <p>We work together as a team to achieve our shared goals and vision.</p>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default CareersPage;
