import React from 'react';
import { useNavigate } from 'react-router-dom';
import SEO from '../components/common/SEO/SEO';
import './ContactPage.css';

const ContactPage = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate('/');
  };

  return (
    <div className="contact-page">
      <SEO
        title="Contact Us - Get Support for AI Chat Platform"
        description="Contact the infini ai support team for help with ChatGPT alternative, Claude alternative, and multi-AI platform. Get assistance with AI chat, subscriptions, and technical support."
        keywords="AI support, ChatGPT support, Claude support, AI chat support, contact AI team, AI platform help, ChatGPT alternative support"
        canonical="/contact"
      />
      {/* Header */}
      <div className="contact-page__header">
        <div className="contact-page__header-content">
          <div className="contact-page__container">
            <button
              onClick={handleGoBack}
              className="contact-page__back-btn"
              aria-label="Go back to home"
            >
              ← Back to Home
            </button>
          </div>
          <h1 className="contact-page__title">Contact Us</h1>
          <p className="contact-page__subtitle">
            Get in touch with us for any inquiries or support
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="contact-page__content">
        <div className="contact-page__container">
          
          {/* Contact Information */}
          <div className="contact-page__info">
            <div className="contact-page__info-grid">
              
              {/* Phone */}
              <div className="contact-page__info-item">
                <div className="contact-page__info-icon">📞</div>
                <h3 className="contact-page__info-title">Phone</h3>
                <a 
                  href="tel:+917892260822" 
                  className="contact-page__info-link"
                >
                  +91 7892260822
                </a>
              </div>

              {/* Email */}
              <div className="contact-page__info-item">
                <div className="contact-page__info-icon">✉️</div>
                <h3 className="contact-page__info-title">Email</h3>
                <a 
                  href="mailto:<EMAIL>" 
                  className="contact-page__info-link"
                >
                  <EMAIL>
                </a>
              </div>

              {/* Address */}
              <div className="contact-page__info-item contact-page__info-item--address">
                <div className="contact-page__info-icon">📍</div>
                <h3 className="contact-page__info-title">Address</h3>
                <div className="contact-page__address">
                  <p>No 17, 3rd Main, PG Halli</p>
                  <p>Bangalore - 560003</p>
                </div>
              </div>

            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default ContactPage;
