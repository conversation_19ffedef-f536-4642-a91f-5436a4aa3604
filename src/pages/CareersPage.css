.careers-page {
  min-height: 100vh;
  background-color: var(--bg-primary, #0a0a0a);
  color: var(--text-primary, #ffffff);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header */
.careers-page__header {
  padding: 2rem 0 4rem;
  text-align: center;
  position: relative;
}

.careers-page__header-content {
  margin: 0 auto;
  padding: 0 1rem;
}

.careers-page__back-btn {
  background: var(--bg-secondary, #1a1a1a);
  border: 1px solid var(--border-color, #333);
  color: var(--text-primary, #ffffff);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  display: flex;
}

.careers-page__back-btn:hover {
  background: var(--bg-hover, #2a2a2a);
  transform: translateY(-2px);
}

.careers-page__title {
  font-size: 3rem;
  font-weight: 700;
  margin: 1rem 0;
  color: var(--text-primary, #ffffff);
}

.careers-page__subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary, #b0b0b0);
  margin: 0;
  font-weight: 300;
}

/* Content */
.careers-page__content {
  padding: 0 1rem 4rem;
}

.careers-page__container {
  max-width: 1000px;
  margin: 0 auto;
}

/* No Positions Section */
.careers-page__no-positions {
  background: var(--bg-secondary, #1a1a1a);
  border-radius: 16px;
  padding: 3rem 2rem;
  text-align: center;
  margin-bottom: 3rem;
  border: 1px solid var(--border-color, #333);
}

.careers-page__icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.careers-page__no-positions-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary, #ffffff);
}

.careers-page__no-positions-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-secondary, #b0b0b0);
  max-width: 600px;
  margin: 0 auto;
}

/* Future Opportunities */
.careers-page__future {
  background: var(--bg-secondary, #1a1a1a);
  border-radius: 16px;
  padding: 2.5rem 2rem;
  margin-bottom: 3rem;
  border: 1px solid var(--border-color, #333);
}

.careers-page__future-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary, #ffffff);
}

.careers-page__future-description {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-secondary, #b0b0b0);
  margin-bottom: 2rem;
}

.careers-page__contact-info {
  background: var(--bg-tertiary, #2a2a2a);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--border-color, #333);
}

.careers-page__contact-info h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary, #ffffff);
}

.careers-page__contact-info p {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-secondary, #b0b0b0);
  margin-bottom: 1.5rem;
}

.careers-page__email-link {
  color: var(--accent-color, #4f46e5);
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: color 0.3s ease;
}

.careers-page__email-link:hover {
  color: var(--accent-hover, #6366f1);
  text-decoration: underline;
}

/* Company Values */
.careers-page__values {
  background: var(--bg-secondary, #1a1a1a);
  border-radius: 16px;
  padding: 2.5rem 2rem;
  border: 1px solid var(--border-color, #333);
}

.careers-page__values-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
  color: var(--text-primary, #ffffff);
}

.careers-page__values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.careers-page__value-item {
  background: var(--bg-tertiary, #2a2a2a);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  border: 1px solid var(--border-color, #333);
  transition: all 0.3s ease;
}

.careers-page__value-item:hover {
  transform: translateY(-4px);
  background: var(--bg-hover, #3a3a3a);
}

.careers-page__value-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.careers-page__value-item h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary, #ffffff);
}

.careers-page__value-item p {
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--text-secondary, #b0b0b0);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .careers-page__title {
    font-size: 2.25rem;
  }
  
  .careers-page__subtitle {
    font-size: 1.1rem;
  }
  
  .careers-page__back-btn {
    margin-bottom: 1rem;
    display: inline-block;
  }
  
  .careers-page__no-positions,
  .careers-page__future,
  .careers-page__values {
    padding: 2rem 1.5rem;
  }
  
  .careers-page__values-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .careers-page__header {
    padding: 1rem 0 2rem;
  }
  
  .careers-page__title {
    font-size: 1.875rem;
  }
  
  .careers-page__no-positions,
  .careers-page__future,
  .careers-page__values {
    padding: 1.5rem 1rem;
  }
}
