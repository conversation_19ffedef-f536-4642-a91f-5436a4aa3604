.contact-page {
  min-height: 100vh;
  background-color: var(--bg-primary, #0a0a0a);
  color: var(--text-primary, #ffffff);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header */
.contact-page__header {
  padding: 2rem 0 4rem;
  text-align: center;
  position: relative;
}

.contact-page__header-content {
  margin: 0 auto;
  padding: 0 1rem;
}

.contact-page__back-btn {
  background: var(--bg-secondary, #1a1a1a);
  border: 1px solid var(--border-color, #333);
  color: var(--text-primary, #ffffff);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  display: flex;
}

.contact-page__back-btn:hover {
  background: var(--bg-hover, #2a2a2a);
  transform: translateY(-2px);
}

.contact-page__title {
  font-size: 3rem;
  font-weight: 700;
  margin: 1rem 0;
  color: var(--text-primary, #ffffff);
}

.contact-page__subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary, #b0b0b0);
  margin: 0;
  font-weight: 300;
}

/* Content */
.contact-page__content {
  padding: 0 1rem 4rem;
}

.contact-page__container {
  max-width: 1000px;
  margin: 0 auto;
}

/* Contact Information */
.contact-page__info {
  background: var(--bg-secondary, #1a1a1a);
  border-radius: 16px;
  padding: 3rem 2rem;
  border: 1px solid var(--border-color, #333);
}

.contact-page__info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 900px;
  margin: 0 auto;
}

.contact-page__info-item {
  background: var(--bg-tertiary, #2a2a2a);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  border: 1px solid var(--border-color, #333);
  transition: all 0.3s ease;
}

.contact-page__info-item:hover {
  transform: translateY(-4px);
  background: var(--bg-hover, #3a3a3a);
}

.contact-page__info-item--address {
  grid-column: 1 / -1;
}

.contact-page__info-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.contact-page__info-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary, #ffffff);
}

.contact-page__info-link {
  color: var(--accent-color, #4f46e5);
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: color 0.3s ease;
  display: inline-block;
}

.contact-page__info-link:hover {
  color: var(--accent-hover, #6366f1);
  text-decoration: underline;
}

.contact-page__address {
  color: var(--text-secondary, #b0b0b0);
  font-size: 1.1rem;
  line-height: 1.6;
}

.contact-page__address p {
  margin: 0.25rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-page__title {
    font-size: 2.25rem;
  }
  
  .contact-page__subtitle {
    font-size: 1.1rem;
  }
  
  .contact-page__back-btn {
    margin-bottom: 1rem;
    display: inline-block;
  }
  
  .contact-page__info {
    padding: 2rem 1.5rem;
  }
  
  .contact-page__info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .contact-page__info-item {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .contact-page__header {
    padding: 1rem 0 2rem;
  }
  
  .contact-page__title {
    font-size: 1.875rem;
  }
  
  .contact-page__info {
    padding: 1.5rem 1rem;
  }
  
  .contact-page__info-item {
    padding: 1.25rem;
  }
  
  .contact-page__info-icon {
    font-size: 2.5rem;
  }
}
