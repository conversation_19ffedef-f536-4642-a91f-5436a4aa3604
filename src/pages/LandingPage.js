import React from 'react';
import {
  Header,
  HeroSection,
  FeaturesSection,
  AboutSection,
  CTASection,
  Footer
} from '../components/landingPage';
import PricingSection from '../components/landingPage/PricingSection/PricingSection';
import SEO from '../components/common/SEO/SEO';

const LandingPage = () => {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "the infini ai",
    "description": "Access ChatGPT, Claude, Gemini, and Llama AI models in one subscription. The ultimate ChatGPT alternative and Claude alternative.",
    "url": "https://theinfiniai.live",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "description": "Free tier available"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "1000"
    }
  };

  return (
    <div className="landing-page">
      <SEO
        title="ChatGPT Alternative, Claude Alternative - All AI Models in One Platform"
        description="Access ChatGPT, Claude, Gemini, and Llama AI models with one subscription. The ultimate ChatGPT alternative and Claude alternative for businesses and individuals. Try all AI chat models in one platform."
        keywords="ChatGPT alternative, Claude alternative, Gemini alternative, Llama AI, AI chat, multi AI platform, AI subscription, GPT alternative, artificial intelligence, AI models, chat AI, AI assistant, OpenAI alternative, Anthropic alternative"
        canonical="/"
        ogTitle="the infini ai - ChatGPT Alternative, Claude Alternative, All AI Models"
        ogDescription="Access ChatGPT, Claude, Gemini, and Llama AI models with one subscription. The ultimate AI platform for all your chat needs."
        structuredData={structuredData}
      />
      <Header />
      <main>
        <HeroSection />
        <CTASection />
        <PricingSection />
        <FeaturesSection />
        <AboutSection />
      </main>
      <Footer />
    </div>
  );
};

export default LandingPage;
