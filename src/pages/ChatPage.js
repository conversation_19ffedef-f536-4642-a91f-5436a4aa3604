import React from 'react';
import { useParams } from 'react-router-dom';
import ChatLayout from '../components/chat/ChatLayout/ChatLayout';
import SEO from '../components/common/SEO/SEO';

const ChatPage = () => {
  const { threadId } = useParams();

  return (
    <div className="chat-page">
      <SEO
        title="AI Chat - ChatGPT, Claude, Gemini, Llama AI Models"
        description="Chat with <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and Llama AI models in one platform. Switch between AI models seamlessly. The best ChatGPT alternative and Claude alternative for AI conversations."
        keywords="AI chat, ChatGPT chat, Claude chat, Gemini chat, Llama chat, AI conversation, ChatGPT alternative, Claude alternative, multi AI chat"
        canonical="/chat"
        noindex={!!threadId} // Don't index individual thread pages
      />
      <ChatLayout threadId={threadId} />
    </div>
  );
};

export default ChatPage;
