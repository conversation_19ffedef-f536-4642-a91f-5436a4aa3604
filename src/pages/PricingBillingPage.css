/* Pricing & Billing Page Styles */
.pricing-billing-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #1a1a1a;
  min-height: 100vh;
  color: #ffffff;
}

.pricing-billing-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #3a3a3a;
  position: relative;
}

.back-btn {
  position: absolute;
  left: 0;
  top: 0;
  background: transparent;
  border: 1px solid #3a3a3a;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #2a2a2a;
  border-color: #fcd469;
  color: #fcd469;
}

.pricing-billing-header h1 {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #ffffff;
}

.pricing-billing-header p {
  font-size: 16px;
  color: #b0b0b0;
  margin: 0;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #3a3a3a;
  border-top: 4px solid #fcd469;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  margin-top: 16px;
  color: #b0b0b0;
  font-size: 16px;
}

/* Error Message */
.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 16px;
  color: #ef4444;
  font-size: 14px;
  margin-bottom: 24px;
  text-align: center;
}

/* Tabs */
.pricing-billing-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 32px;
  background: #2a2a2a;
  border-radius: 12px;
  padding: 4px;
  border: 1px solid #3a3a3a;
}

.tab-btn {
  flex: 1;
  background: transparent;
  border: none;
  color: #b0b0b0;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.tab-btn:hover {
  background: #3a3a3a;
  color: #ffffff;
}

.tab-btn.active {
  background: #fcd469;
  color: #1a1a1a;
}

.tab-icon {
  font-size: 16px;
}

.tab-label {
  font-weight: 500;
}

/* Content */
.pricing-billing-content {
  min-height: 400px;
}

/* Auth Required */
.auth-required {
  text-align: center;
  padding: 60px 20px;
  background: #2a2a2a;
  border-radius: 12px;
  border: 1px solid #3a3a3a;
}

.auth-required h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #ffffff;
}

.auth-required p {
  font-size: 16px;
  color: #b0b0b0;
  margin: 0 0 24px 0;
}

.signin-btn {
  background: #fcd469;
  color: #1a1a1a;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.signin-btn:hover {
  background: #f5c842;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .pricing-billing-container {
    padding: 15px;
  }

  .pricing-billing-header {
    padding-top: 50px;
  }

  .back-btn {
    position: static;
    margin-bottom: 20px;
    align-self: flex-start;
  }

  .pricing-billing-tabs {
    flex-direction: column;
    gap: 8px;
  }

  .tab-btn {
    justify-content: flex-start;
    padding: 16px;
  }

  .pricing-billing-header h1 {
    font-size: 28px;
  }
}
