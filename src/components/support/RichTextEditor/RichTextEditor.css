/* Rich Text Editor Container */
.rich-text-editor {
  width: 100%;
}

.rich-text-editor__container {
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--card-bg);
  transition: border-color 0.2s ease;
}

.rich-text-editor__container:focus-within {
  border-color: var(--accent-color);
}

.rich-text-editor--error .rich-text-editor__container {
  border-color: #ef4444;
}

/* Quill Editor Customization */
.rich-text-editor__quill .ql-toolbar {
  border: none;
  border-bottom: 1px solid var(--border-color);
  background: var(--secondary-bg);
  border-radius: 8px 8px 0 0;
  padding: 8px 12px;
}

.rich-text-editor__quill .ql-container {
  border: none;
  font-family: inherit;
  font-size: 14px;
  border-radius: 0 0 8px 8px;
}

.rich-text-editor__quill .ql-editor {
  min-height: 120px;
  max-height: 300px;
  overflow-y: auto;
  color: var(--text-primary);
  background: var(--card-bg);
  padding: 16px;
  line-height: 1.6;
}

.rich-text-editor__quill .ql-editor.ql-blank::before {
  color: var(--text-muted);
  font-style: normal;
}

.rich-text-editor__quill--disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* Toolbar Button Styling */
.rich-text-editor__quill .ql-toolbar .ql-formats {
  margin-right: 12px;
}

.rich-text-editor__quill .ql-toolbar button {
  color: var(--text-secondary);
  border-radius: 4px;
  padding: 4px;
  margin: 0 1px;
  transition: all 0.2s ease;
}

.rich-text-editor__quill .ql-toolbar button:hover {
  color: var(--text-primary);
  background: var(--hover-bg);
}

.rich-text-editor__quill .ql-toolbar button.ql-active {
  color: var(--accent-color);
  background: rgba(252, 212, 105, 0.1);
}

.rich-text-editor__quill .ql-toolbar .ql-picker {
  color: var(--text-secondary);
}

.rich-text-editor__quill .ql-toolbar .ql-picker:hover {
  color: var(--text-primary);
}

.rich-text-editor__quill .ql-toolbar .ql-picker.ql-expanded {
  color: var(--text-primary);
}

.rich-text-editor__quill .ql-toolbar .ql-picker-options {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
}

.rich-text-editor__quill .ql-toolbar .ql-picker-item {
  color: var(--text-primary);
  padding: 6px 12px;
}

.rich-text-editor__quill .ql-toolbar .ql-picker-item:hover {
  background: var(--hover-bg);
}

/* Content Styling */
.rich-text-editor__quill .ql-editor h1,
.rich-text-editor__quill .ql-editor h2,
.rich-text-editor__quill .ql-editor h3 {
  color: var(--text-primary);
  margin: 16px 0 8px 0;
  font-weight: 600;
}

.rich-text-editor__quill .ql-editor h1 {
  font-size: 1.5em;
}

.rich-text-editor__quill .ql-editor h2 {
  font-size: 1.3em;
}

.rich-text-editor__quill .ql-editor h3 {
  font-size: 1.1em;
}

.rich-text-editor__quill .ql-editor p {
  margin: 8px 0;
}

.rich-text-editor__quill .ql-editor ul,
.rich-text-editor__quill .ql-editor ol {
  margin: 8px 0;
  padding-left: 24px;
}

.rich-text-editor__quill .ql-editor li {
  margin: 4px 0;
}

.rich-text-editor__quill .ql-editor a {
  color: var(--accent-color);
  text-decoration: underline;
}

.rich-text-editor__quill .ql-editor a:hover {
  color: #e6b800;
}

.rich-text-editor__quill .ql-editor strong {
  font-weight: 600;
}

.rich-text-editor__quill .ql-editor em {
  font-style: italic;
}

/* Footer */
.rich-text-editor__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding: 0 4px;
}

.rich-text-editor__info {
  flex: 1;
}

.rich-text-editor__error {
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
}

.rich-text-editor__counter {
  font-size: 12px;
  color: var(--text-muted);
  font-weight: 500;
  transition: color 0.2s ease;
}

.rich-text-editor__counter--warning {
  color: #f59e0b;
}

.rich-text-editor__counter--error {
  color: #ef4444;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .rich-text-editor__quill .ql-snow .ql-tooltip {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
  }

  .rich-text-editor__quill .ql-snow .ql-tooltip input[type=text] {
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
  }

  .rich-text-editor__quill .ql-snow .ql-tooltip a.ql-action::after {
    color: var(--accent-color);
  }

  .rich-text-editor__quill .ql-snow .ql-tooltip a.ql-remove::before {
    color: #ef4444;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .rich-text-editor__quill .ql-toolbar {
    padding: 6px 8px;
  }

  .rich-text-editor__quill .ql-toolbar .ql-formats {
    margin-right: 8px;
  }

  .rich-text-editor__quill .ql-editor {
    padding: 12px;
    min-height: 100px;
  }

  .rich-text-editor__footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
