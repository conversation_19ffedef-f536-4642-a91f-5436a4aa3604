import React, { useMemo } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import './RichTextEditor.css';

const RichTextEditor = ({
  value,
  onChange,
  placeholder = 'Enter description...',
  disabled = false,
  error = null,
  maxLength = 5000,
  className = ''
}) => {
  // Configure toolbar modules
  const modules = useMemo(() => ({
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link'],
      [{ 'color': [] }, { 'background': [] }],
      ['clean']
    ],
    clipboard: {
      matchVisual: false,
    }
  }), []);

  // Configure formats
  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent',
    'link',
    'color', 'background'
  ];

  // Handle content change
  const handleChange = (content, delta, source, editor) => {
    const text = editor.getText();
    
    // Check length limit
    if (text.length <= maxLength) {
      onChange(content);
    }
  };

  // Get character count
  const getCharacterCount = () => {
    if (!value) return 0;
    // Create a temporary div to extract text content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = value;
    return tempDiv.textContent?.length || 0;
  };

  const characterCount = getCharacterCount();
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;

  return (
    <div className={`rich-text-editor ${className} ${error ? 'rich-text-editor--error' : ''}`}>
      <div className="rich-text-editor__container">
        <ReactQuill
          theme="snow"
          value={value || ''}
          onChange={handleChange}
          modules={modules}
          formats={formats}
          placeholder={placeholder}
          readOnly={disabled}
          className={`rich-text-editor__quill ${disabled ? 'rich-text-editor__quill--disabled' : ''}`}
        />
      </div>
      
      <div className="rich-text-editor__footer">
        <div className="rich-text-editor__info">
          {error && (
            <span className="rich-text-editor__error">
              {error}
            </span>
          )}
        </div>
        
        <div className={`rich-text-editor__counter ${isNearLimit ? 'rich-text-editor__counter--warning' : ''} ${isOverLimit ? 'rich-text-editor__counter--error' : ''}`}>
          {characterCount} / {maxLength}
        </div>
      </div>
    </div>
  );
};

export default RichTextEditor;
