/* Ticket Details Component */
.ticket-details {
  max-width: 800px;
  margin: 0 auto;
}

/* Loading State */
.ticket-details__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-muted);
}

.ticket-details__spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

/* Error State */
.ticket-details__error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #ef4444;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  color: #ef4444;
}

.ticket-details__retry-btn {
  margin-top: 12px;
  padding: 8px 16px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.ticket-details__retry-btn:hover {
  background: #dc2626;
}

/* Header */
.ticket-details__header {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.ticket-details__header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 16px;
}

.ticket-details__subject {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  flex: 1;
}

.ticket-details__badges {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.ticket-details__priority-badge,
.ticket-details__status-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
}

.ticket-details__meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.ticket-details__meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ticket-details__meta-label {
  font-size: 12px;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  min-width: 80px;
}

.ticket-details__meta-value {
  font-size: 14px;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.ticket-details__ticket-id {
  font-family: monospace;
  background: var(--secondary-bg);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  word-break: break-all;
}

.ticket-details__copy-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 14px;
}

.ticket-details__copy-btn:hover {
  background: var(--hover-bg);
}

/* Content */
.ticket-details__content {
  margin-bottom: 24px;
}

.ticket-details__section {
  margin-bottom: 24px;
}

.ticket-details__section-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

/* Description */
.ticket-details__description {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
}

.ticket-details__description-content {
  color: var(--text-primary);
  line-height: 1.6;
}

.ticket-details__description-content h1,
.ticket-details__description-content h2,
.ticket-details__description-content h3 {
  color: var(--text-primary);
  margin: 16px 0 8px 0;
}

.ticket-details__description-content p {
  margin: 8px 0;
}

.ticket-details__description-content ul,
.ticket-details__description-content ol {
  margin: 8px 0;
  padding-left: 24px;
}

.ticket-details__description-content li {
  margin: 4px 0;
}

.ticket-details__description-content a {
  color: var(--accent-color);
  text-decoration: underline;
}

.ticket-details__description-content strong {
  font-weight: 600;
}

.ticket-details__description-content em {
  font-style: italic;
}

/* Technical Details */
.ticket-details__tech-details {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
}

.ticket-details__tech-details pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: var(--text-primary);
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
}

/* Attachment */
.ticket-details__attachment {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
}

.ticket-details__attachment-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.ticket-details__attachment-details {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.ticket-details__attachment-icon {
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 8px;
}

.ticket-details__attachment-meta {
  flex: 1;
}

.ticket-details__attachment-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
  word-break: break-all;
}

.ticket-details__attachment-size {
  font-size: 12px;
  color: var(--text-muted);
}

.ticket-details__attachment-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: var(--accent-color);
  color: var(--primary-bg);
  border: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.ticket-details__attachment-btn:hover {
  background: #e6b800;
  transform: translateY(-1px);
}

/* User Information */
.ticket-details__user-info {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
}

.ticket-details__user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.ticket-details__user-item:last-child {
  margin-bottom: 0;
}

.ticket-details__user-label {
  font-size: 12px;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  min-width: 60px;
}

.ticket-details__user-value {
  font-size: 14px;
  color: var(--text-primary);
}

/* Footer */
.ticket-details__footer {
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.ticket-details__footer-info {
  margin-bottom: 16px;
}

.ticket-details__footer-text {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: var(--text-muted);
  line-height: 1.5;
}

.ticket-details__email-link {
  color: var(--accent-color);
  text-decoration: none;
}

.ticket-details__email-link:hover {
  text-decoration: underline;
}

.ticket-details__actions {
  display: flex;
  justify-content: flex-end;
}

.ticket-details__close-btn {
  padding: 10px 20px;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ticket-details__close-btn:hover {
  background: var(--hover-bg);
  border-color: var(--accent-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .ticket-details__header-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .ticket-details__badges {
    align-self: flex-end;
  }

  .ticket-details__meta {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .ticket-details__meta-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .ticket-details__meta-label {
    min-width: auto;
  }

  .ticket-details__user-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .ticket-details__user-label {
    min-width: auto;
  }

  .ticket-details__actions {
    justify-content: stretch;
  }

  .ticket-details__close-btn {
    width: 100%;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
