import React, { useState, useEffect } from 'react';
import supportService from '../../../services/supportService';
import './TicketDetails.css';

const TicketDetails = ({ ticket: initialTicket, onClose }) => {
  const [ticket, setTicket] = useState(initialTicket);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (initialTicket?.id) {
      loadTicketDetails(initialTicket.id);
    }
  }, [initialTicket?.id]);

  const loadTicketDetails = async (ticketId) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await supportService.getTicketById(ticketId);
      
      if (response.success) {
        setTicket(response.data.ticket);
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const copyTicketId = () => {
    navigator.clipboard.writeText(ticket.id);
    // You could add a toast notification here
  };

  const openAttachment = () => {
    // Use the new attachment structure with url field
    if (ticket.attachment?.url) {
      const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5529/api';
      const fullUrl = `${baseUrl}${ticket.attachment.url}`;
      window.open(fullUrl, '_blank');
    } else if (ticket.attachmentInfo?.secureFileId) {
      // Fallback for old structure
      const secureUrl = supportService.getSecureFileUrl(ticket.attachmentInfo.secureFileId);
      window.open(secureUrl, '_blank');
    } else if (ticket.attachmentUrl) {
      // Fallback for legacy structure
      window.open(ticket.attachmentUrl, '_blank');
    }
  };

  const renderPriorityBadge = (priority) => {
    const priorityInfo = supportService.formatPriority(priority);
    return (
      <span
        className="ticket-details__priority-badge"
        style={{ backgroundColor: priorityInfo.color }}
      >
        {priorityInfo.label}
      </span>
    );
  };

  const renderStatusBadge = (status) => {
    const statusInfo = supportService.formatStatus(status);
    return (
      <span
        className="ticket-details__status-badge"
        style={{ backgroundColor: statusInfo.color }}
      >
        {statusInfo.label}
      </span>
    );
  };

  const renderDescription = (description) => {
    return (
      <div
        className="ticket-details__description-content"
        dangerouslySetInnerHTML={{ __html: description }}
      />
    );
  };

  if (loading) {
    return (
      <div className="ticket-details">
        <div className="ticket-details__loading">
          <div className="ticket-details__spinner"></div>
          <p>Loading ticket details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="ticket-details">
        <div className="ticket-details__error">
          <p>Error loading ticket details: {error}</p>
          <button
            onClick={() => loadTicketDetails(ticket.id)}
            className="ticket-details__retry-btn"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!ticket) {
    return (
      <div className="ticket-details">
        <div className="ticket-details__error">
          <p>Ticket not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="ticket-details">
      {/* Header */}
      <div className="ticket-details__header">
        <div className="ticket-details__header-main">
          <h2 className="ticket-details__subject">{ticket.subject}</h2>
          <div className="ticket-details__badges">
            {renderPriorityBadge(ticket.priority)}
            {renderStatusBadge(ticket.status)}
          </div>
        </div>
        
        <div className="ticket-details__meta">
          <div className="ticket-details__meta-item">
            <span className="ticket-details__meta-label">Ticket ID:</span>
            <span className="ticket-details__meta-value">
              <code className="ticket-details__ticket-id">
                {ticket.id}
              </code>
              <button
                onClick={copyTicketId}
                className="ticket-details__copy-btn"
                title="Copy ticket ID"
              >
                📋
              </button>
            </span>
          </div>
          
          <div className="ticket-details__meta-item">
            <span className="ticket-details__meta-label">Created:</span>
            <span className="ticket-details__meta-value">
              {supportService.formatDate(ticket.createdAt)}
            </span>
          </div>
          
          <div className="ticket-details__meta-item">
            <span className="ticket-details__meta-label">Last Updated:</span>
            <span className="ticket-details__meta-value">
              {supportService.formatDate(ticket.updatedAt)}
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="ticket-details__content">
        {/* Description */}
        <div className="ticket-details__section">
          <h3 className="ticket-details__section-title">Description</h3>
          <div className="ticket-details__description">
            {renderDescription(ticket.description)}
          </div>
        </div>

        {/* Technical Details */}
        {ticket.techDetails && (
          <div className="ticket-details__section">
            <h3 className="ticket-details__section-title">Technical Details</h3>
            <div className="ticket-details__tech-details">
              <pre>{ticket.techDetails}</pre>
            </div>
          </div>
        )}

        {/* Attachment */}
        {(ticket.attachment || ticket.attachmentUrl || ticket.attachmentInfo) && (
          <div className="ticket-details__section">
            <h3 className="ticket-details__section-title">Attachment</h3>
            <div className="ticket-details__attachment">
              {ticket.attachment ? (
                <div className="ticket-details__attachment-info">
                  <div className="ticket-details__attachment-details">
                    <div className="ticket-details__attachment-icon">
                      {supportService.getFileIcon(ticket.attachment.type)}
                    </div>
                    <div className="ticket-details__attachment-meta">
                      <div className="ticket-details__attachment-name">
                        {ticket.attachment.name}
                      </div>
                      <div className="ticket-details__attachment-size">
                        {supportService.formatFileSize(ticket.attachment.size)}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={openAttachment}
                    className="ticket-details__attachment-btn"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M13.8 12H3"/>
                    </svg>
                    Open File
                  </button>
                </div>
              ) : ticket.attachmentInfo ? (
                <div className="ticket-details__attachment-info">
                  <div className="ticket-details__attachment-details">
                    <div className="ticket-details__attachment-icon">
                      {supportService.getFileIcon(ticket.attachmentInfo.mimeType)}
                    </div>
                    <div className="ticket-details__attachment-meta">
                      <div className="ticket-details__attachment-name">
                        {ticket.attachmentInfo.originalName}
                      </div>
                      <div className="ticket-details__attachment-size">
                        {supportService.formatFileSize(ticket.attachmentInfo.size)}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={openAttachment}
                    className="ticket-details__attachment-btn"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M13.8 12H3"/>
                    </svg>
                    Open File
                  </button>
                </div>
              ) : (
                <button
                  onClick={openAttachment}
                  className="ticket-details__attachment-btn"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
                  </svg>
                  View Attachment
                </button>
              )}
            </div>
          </div>
        )}

        {/* User Information */}
        {ticket.user && (
          <div className="ticket-details__section">
            <h3 className="ticket-details__section-title">Contact Information</h3>
            <div className="ticket-details__user-info">
              <div className="ticket-details__user-item">
                <span className="ticket-details__user-label">Email:</span>
                <span className="ticket-details__user-value">{ticket.user.email}</span>
              </div>
              {ticket.user.mobile && (
                <div className="ticket-details__user-item">
                  <span className="ticket-details__user-label">Mobile:</span>
                  <span className="ticket-details__user-value">{ticket.user.mobile}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="ticket-details__footer">
        <div className="ticket-details__footer-info">
          <p className="ticket-details__footer-text">
            📧 You will receive email notifications for any updates to this ticket.
          </p>
          <p className="ticket-details__footer-text">
            💬 For urgent matters, please contact support directly at{' '}
            <a href="mailto:<EMAIL>" className="ticket-details__email-link">
              <EMAIL>
            </a>
          </p>
        </div>
        
        <div className="ticket-details__actions">
          <button
            onClick={onClose}
            className="ticket-details__close-btn"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default TicketDetails;
