import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import supportService from '../../../services/supportService';
import CreateTicket from '../CreateTicket/CreateTicket';
import TicketList from '../TicketList/TicketList';
import SEO from '../../common/SEO/SEO';
import './SupportPage.css';

const SupportPage = () => {
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('create');
  const [supportInfo, setSupportInfo] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [loading, setLoading] = useState(true);

  const loadSupportInfo = async () => {
    try {
      setLoading(true);
      const response = await supportService.getSupportInfo();

      if (response.success) {
        setSupportInfo(response.data);
      }
    } catch (error) {
      console.error('Error loading support info:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSupportInfo();
  }, []);

  const handleTicketCreated = (ticket) => {
    // Switch to tickets tab and refresh the list
    setActiveTab('tickets');
    setRefreshTrigger(prev => prev + 1);
  };

  const handleTabChange = (tab) => {
    if (!isAuthenticated && (tab === 'create' || tab === 'tickets')) {
      navigate('/signin');
      return;
    }
    setActiveTab(tab);
  };

  const renderResponseTimes = () => {
    if (!supportInfo?.responseTime?.times) return null;

    return (
      <div className="support-page__response-times">
        <h3>📅 Expected Response Times</h3>
        <div className="support-page__response-grid">
          {supportInfo.responseTime.times.map((item, index) => (
            <div key={index} className="support-page__response-item">
              <div className="support-page__response-priority">
                <span
                  className="support-page__priority-indicator"
                  style={{
                    backgroundColor: supportService.formatPriority(item.priority).color
                  }}
                ></span>
                {item.priority}
              </div>
              <div className="support-page__response-time">{item.time}</div>
              <div className="support-page__response-desc">{item.description}</div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderContactInfo = () => {
    if (!supportInfo?.contact) return null;

    return (
      <div className="support-page__contact">
        <h3>📞 Contact Information</h3>
        <div className="support-page__contact-grid">
          <div className="support-page__contact-item">
            <strong>Email:</strong>
            <a href={`mailto:${supportInfo.contact.email}`} className="support-page__email-link">
              {supportInfo.contact.email}
            </a>
          </div>
          <div className="support-page__contact-item">
            <strong>Business Hours:</strong>
            <span>{supportInfo.contact.businessHours}</span>
          </div>
          <div className="support-page__contact-item">
            <strong>Emergency Contact:</strong>
            <span>{supportInfo.contact.emergencyContact}</span>
          </div>
        </div>
      </div>
    );
  };

  const renderTips = () => {
    if (!supportInfo?.tips) return null;

    return (
      <div className="support-page__tips">
        <h3>💡 Helpful Tips</h3>
        <ul className="support-page__tips-list">
          {supportInfo.tips.map((tip, index) => (
            <li key={index}>{tip}</li>
          ))}
        </ul>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="support-page">
        <div className="support-page__loading">
          <div className="support-page__spinner"></div>
          <p>Loading support information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="support-page">
      <SEO
        title="Support - Get Help with AI Platform"
        description="Get support for the infini ai platform. Help with ChatGPT alternative, Claude alternative, AI chat issues, billing, and technical support. Contact our expert team."
        keywords="AI support, ChatGPT support, Claude support, AI platform help, AI chat support, technical support, billing support, customer service"
        canonical="/support"
      />
      {/* Header */}
      <div className="support-page__header">
        <div className="support-page__header-content">
          <h1 className="support-page__title">🎫 Customer Support</h1>
          <p className="support-page__subtitle">
            Get help with your account, billing, technical issues, and more.
          </p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="support-page__nav">
        <div className="support-page__nav-container">
          <button
            onClick={() => handleTabChange('create')}
            className={`support-page__nav-btn ${activeTab === 'create' ? 'support-page__nav-btn--active' : ''}`}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 5v14M5 12h14"/>
            </svg>
            Create Ticket
          </button>
          
          <button
            onClick={() => handleTabChange('tickets')}
            className={`support-page__nav-btn ${activeTab === 'tickets' ? 'support-page__nav-btn--active' : ''}`}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
              <line x1="16" y1="13" x2="8" y2="13"/>
              <line x1="16" y1="17" x2="8" y2="17"/>
              <polyline points="10,9 9,9 8,9"/>
            </svg>
            My Tickets
          </button>
          
          <button
            onClick={() => handleTabChange('guidelines')}
            className={`support-page__nav-btn ${activeTab === 'guidelines' ? 'support-page__nav-btn--active' : ''}`}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="10"/>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
              <line x1="12" y1="17" x2="12.01" y2="17"/>
            </svg>
            Guidelines
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="support-page__content">
        {activeTab === 'create' && (
          <div className="support-page__tab-content">
            {!isAuthenticated ? (
              <div className="support-page__auth-required">
                <div className="support-page__auth-icon">🔐</div>
                <h3>Authentication Required</h3>
                <p>Please sign in to create a support ticket.</p>
                <button
                  onClick={() => navigate('/signin')}
                  className="support-page__signin-btn"
                >
                  Sign In
                </button>
              </div>
            ) : (
              <CreateTicket onTicketCreated={handleTicketCreated} />
            )}
          </div>
        )}

        {activeTab === 'tickets' && (
          <div className="support-page__tab-content">
            {!isAuthenticated ? (
              <div className="support-page__auth-required">
                <div className="support-page__auth-icon">🔐</div>
                <h3>Authentication Required</h3>
                <p>Please sign in to view your support tickets.</p>
                <button
                  onClick={() => navigate('/signin')}
                  className="support-page__signin-btn"
                >
                  Sign In
                </button>
              </div>
            ) : (
              <TicketList refreshTrigger={refreshTrigger} />
            )}
          </div>
        )}

        {activeTab === 'guidelines' && (
          <div className="support-page__tab-content">
            <div className="support-page__guidelines">
              {supportInfo?.guidelines && (
                <div className="support-page__guidelines-section">
                  <h2>{supportInfo.guidelines.title}</h2>
                  <ul className="support-page__guidelines-list">
                    {supportInfo.guidelines.items.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              )}

              {renderResponseTimes()}
              {renderContactInfo()}
              {renderTips()}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SupportPage;
