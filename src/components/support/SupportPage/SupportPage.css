/* Support Page Component */
.support-page {
  min-height: 100vh;
  background: var(--primary-bg);
  color: var(--text-primary);
}

/* Loading State */
.support-page__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-muted);
}

.support-page__spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* Header */
.support-page__header {
  background: var(--secondary-bg);
  border-bottom: 1px solid var(--border-color);
  padding: 40px 0;
}

.support-page__header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.support-page__title {
  margin: 0 0 12px 0;
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
}

.support-page__subtitle {
  margin: 0;
  font-size: 16px;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Navigation */
.support-page__nav {
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  padding: 0 24px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.support-page__nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  gap: 0;
}

.support-page__nav-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 24px;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
  position: relative;
}

.support-page__nav-btn:hover {
  color: var(--text-primary);
  background: var(--hover-bg);
}

.support-page__nav-btn--active {
  color: var(--accent-color);
  border-bottom-color: var(--accent-color);
}

.support-page__nav-btn--active:hover {
  color: var(--accent-color);
}

/* Content */
.support-page__content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 24px;
}

.support-page__tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Auth Required */
.support-page__auth-required {
  text-align: center;
  padding: 60px 20px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  max-width: 500px;
  margin: 0 auto;
}

.support-page__auth-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.support-page__auth-required h3 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 20px;
}

.support-page__auth-required p {
  margin: 0 0 24px 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.support-page__signin-btn {
  padding: 12px 24px;
  background: var(--accent-color);
  color: var(--primary-bg);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.support-page__signin-btn:hover {
  background: #e6b800;
  transform: translateY(-1px);
}

/* Guidelines */
.support-page__guidelines {
  max-width: 800px;
  margin: 0 auto;
}

.support-page__guidelines-section {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.support-page__guidelines-section h2 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.support-page__guidelines-list {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
  line-height: 1.6;
}

.support-page__guidelines-list li {
  margin-bottom: 8px;
}

/* Response Times */
.support-page__response-times {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.support-page__response-times h3 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.support-page__response-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.support-page__response-item {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.support-page__response-priority {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--text-primary);
}

.support-page__priority-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.support-page__response-time {
  font-size: 18px;
  font-weight: 700;
  color: var(--accent-color);
  margin-bottom: 4px;
}

.support-page__response-desc {
  font-size: 12px;
  color: var(--text-muted);
  line-height: 1.4;
}

/* Contact Information */
.support-page__contact {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.support-page__contact h3 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.support-page__contact-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.support-page__contact-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.support-page__contact-item strong {
  font-size: 12px;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.support-page__contact-item span,
.support-page__email-link {
  font-size: 14px;
  color: var(--text-primary);
}

.support-page__email-link {
  color: var(--accent-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

.support-page__email-link:hover {
  color: #e6b800;
  text-decoration: underline;
}

/* Tips */
.support-page__tips {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.support-page__tips h3 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.support-page__tips-list {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
  line-height: 1.6;
}

.support-page__tips-list li {
  margin-bottom: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .support-page__header {
    padding: 24px 0;
  }

  .support-page__header-content {
    padding: 0 16px;
  }

  .support-page__title {
    font-size: 24px;
  }

  .support-page__nav {
    padding: 0 16px;
  }

  .support-page__nav-container {
    flex-direction: column;
    gap: 0;
  }

  .support-page__nav-btn {
    padding: 12px 16px;
    border-bottom: none;
    border-left: 3px solid transparent;
  }

  .support-page__nav-btn--active {
    border-bottom: none;
    border-left-color: var(--accent-color);
    background: var(--hover-bg);
  }

  .support-page__content {
    padding: 24px 16px;
  }

  .support-page__response-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .support-page__response-item {
    padding: 12px;
  }

  .support-page__guidelines-section,
  .support-page__response-times,
  .support-page__contact,
  .support-page__tips {
    padding: 20px;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
