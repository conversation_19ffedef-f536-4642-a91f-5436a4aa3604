/* Ticket List Component */
.ticket-list {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
}

/* Loading State */
.ticket-list__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-muted);
}

.ticket-list__spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* Stats Section */
.ticket-list__stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.ticket-list__stat {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.ticket-list__stat-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.ticket-list__stat-label {
  font-size: 12px;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Filters Section */
.ticket-list__filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.ticket-list__filter-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.ticket-list__filter-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ticket-list__filter-select:focus {
  outline: none;
  border-color: var(--accent-color);
}

.ticket-list__clear-filters {
  padding: 8px 16px;
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-secondary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ticket-list__clear-filters:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

/* Error State */
.ticket-list__error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #ef4444;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  color: #ef4444;
}

.ticket-list__retry-btn {
  margin-top: 12px;
  padding: 8px 16px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.ticket-list__retry-btn:hover {
  background: #dc2626;
}

/* Empty State */
.ticket-list__empty {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-muted);
}

.ticket-list__empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.ticket-list__empty h3 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 18px;
}

.ticket-list__empty p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Ticket Items */
.ticket-list__items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.ticket-list__item {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ticket-list__item:hover {
  border-color: var(--accent-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ticket-list__item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 16px;
}

.ticket-list__item-subject {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  flex: 1;
}

.ticket-list__item-badges {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.ticket-list__priority-badge,
.ticket-list__status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: white;
}

.ticket-list__item-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 12px;
  color: var(--text-muted);
}

.ticket-list__item-id {
  font-family: monospace;
  background: var(--secondary-bg);
  padding: 2px 6px;
  border-radius: 4px;
}

.ticket-list__item-preview {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

/* Pagination */
.ticket-list__pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.ticket-list__page-btn {
  padding: 8px 12px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
}

.ticket-list__page-btn:hover:not(:disabled) {
  background: var(--hover-bg);
  border-color: var(--accent-color);
}

.ticket-list__page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ticket-list__page-btn--active {
  background: var(--accent-color);
  color: var(--primary-bg);
  border-color: var(--accent-color);
}

.ticket-list__page-btn--active:hover {
  background: #e6b800;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ticket-list {
    padding: 16px;
  }

  .ticket-list__stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .ticket-list__stat {
    padding: 16px;
  }

  .ticket-list__filters {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .ticket-list__filter-group {
    flex-direction: column;
    gap: 8px;
  }

  .ticket-list__filter-select {
    width: 100%;
  }

  .ticket-list__item {
    padding: 16px;
  }

  .ticket-list__item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .ticket-list__item-badges {
    align-self: flex-end;
  }

  .ticket-list__pagination {
    flex-wrap: wrap;
    gap: 4px;
  }

  .ticket-list__page-btn {
    padding: 6px 10px;
    font-size: 12px;
    min-width: 36px;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
