import React, { useState, useEffect } from 'react';
import supportService from '../../../services/supportService';
import TicketDetails from '../TicketDetails/TicketDetails';
import { Modal } from '../../common';
import './TicketList.css';

const TicketList = ({ refreshTrigger }) => {
  const [tickets, setTickets] = useState([]);
  const [pagination, setPagination] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    page: 1,
    limit: 10
  });
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showTicketModal, setShowTicketModal] = useState(false);
  const [statuses, setStatuses] = useState([]);
  const [priorities, setPriorities] = useState([]);
  const [stats, setStats] = useState(null);

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    loadTickets();
  }, [filters, refreshTrigger]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadInitialData = async () => {
    try {
      const [statusesResponse, prioritiesResponse, statsResponse] = await Promise.all([
        supportService.getStatuses(),
        supportService.getPriorities(),
        supportService.getTicketStats()
      ]);

      if (statusesResponse.success) {
        setStatuses(statusesResponse.data.statuses);
      }

      if (prioritiesResponse.success) {
        setPriorities(prioritiesResponse.data.priorities);
      }

      if (statsResponse.success) {
        setStats(statsResponse.data.stats);
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  };

  const loadTickets = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: filters.page,
        limit: filters.limit,
        ...(filters.status && { status: filters.status }),
        ...(filters.priority && { priority: filters.priority })
      };

      const response = await supportService.getTickets(params);

      if (response.success) {
        setTickets(response.data.tickets);
        setPagination(response.data.pagination);
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({
      ...prev,
      page: newPage
    }));
  };

  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);
    setShowTicketModal(true);
  };

  const handleCloseTicketModal = () => {
    setShowTicketModal(false);
    setSelectedTicket(null);
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      priority: '',
      page: 1,
      limit: 10
    });
  };

  const renderPriorityBadge = (priority) => {
    const priorityInfo = supportService.formatPriority(priority);
    return (
      <span
        className="ticket-list__priority-badge"
        style={{ backgroundColor: priorityInfo.color }}
      >
        {priorityInfo.label}
      </span>
    );
  };

  const renderStatusBadge = (status) => {
    const statusInfo = supportService.formatStatus(status);
    return (
      <span
        className="ticket-list__status-badge"
        style={{ backgroundColor: statusInfo.color }}
      >
        {statusInfo.label}
      </span>
    );
  };

  const renderPagination = () => {
    if (!pagination || pagination.totalPages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 5;
    const currentPage = pagination.currentPage;
    const totalPages = pagination.totalPages;

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`ticket-list__page-btn ${i === currentPage ? 'ticket-list__page-btn--active' : ''}`}
        >
          {i}
        </button>
      );
    }

    return (
      <div className="ticket-list__pagination">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={!pagination.hasPreviousPage}
          className="ticket-list__page-btn"
        >
          ← Previous
        </button>
        {pages}
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={!pagination.hasNextPage}
          className="ticket-list__page-btn"
        >
          Next →
        </button>
      </div>
    );
  };

  if (loading && tickets.length === 0) {
    return (
      <div className="ticket-list">
        <div className="ticket-list__loading">
          <div className="ticket-list__spinner"></div>
          <p>Loading tickets...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="ticket-list">
      {/* Stats Section */}
      {stats && (
        <div className="ticket-list__stats">
          <div className="ticket-list__stat">
            <span className="ticket-list__stat-value">{stats.total}</span>
            <span className="ticket-list__stat-label">Total Tickets</span>
          </div>
          <div className="ticket-list__stat">
            <span className="ticket-list__stat-value">{stats.open}</span>
            <span className="ticket-list__stat-label">Open</span>
          </div>
          <div className="ticket-list__stat">
            <span className="ticket-list__stat-value">{stats.resolved}</span>
            <span className="ticket-list__stat-label">Resolved</span>
          </div>
          <div className="ticket-list__stat">
            <span className="ticket-list__stat-value">{stats.urgent}</span>
            <span className="ticket-list__stat-label">Urgent</span>
          </div>
        </div>
      )}

      {/* Filters Section */}
      <div className="ticket-list__filters">
        <div className="ticket-list__filter-group">
          <select
            value={filters.status}
            onChange={(e) => handleFilterChange('status', e.target.value)}
            className="ticket-list__filter-select"
          >
            <option value="">All Statuses</option>
            {statuses.map(status => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>

          <select
            value={filters.priority}
            onChange={(e) => handleFilterChange('priority', e.target.value)}
            className="ticket-list__filter-select"
          >
            <option value="">All Priorities</option>
            {priorities.map(priority => (
              <option key={priority.value} value={priority.value}>
                {priority.label}
              </option>
            ))}
          </select>

          <select
            value={filters.limit}
            onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
            className="ticket-list__filter-select"
          >
            <option value={5}>5 per page</option>
            <option value={10}>10 per page</option>
            <option value={20}>20 per page</option>
            <option value={50}>50 per page</option>
          </select>
        </div>

        {(filters.status || filters.priority) && (
          <button
            onClick={clearFilters}
            className="ticket-list__clear-filters"
          >
            Clear Filters
          </button>
        )}
      </div>

      {/* Error State */}
      {error && (
        <div className="ticket-list__error">
          <p>Error loading tickets: {error}</p>
          <button onClick={loadTickets} className="ticket-list__retry-btn">
            Try Again
          </button>
        </div>
      )}

      {/* Tickets List */}
      {!error && (
        <>
          {tickets.length === 0 ? (
            <div className="ticket-list__empty">
              <div className="ticket-list__empty-icon">🎫</div>
              <h3>No tickets found</h3>
              <p>
                {filters.status || filters.priority
                  ? 'No tickets match your current filters.'
                  : 'You haven\'t created any support tickets yet.'}
              </p>
            </div>
          ) : (
            <div className="ticket-list__items">
              {tickets.map(ticket => (
                <div
                  key={ticket.id}
                  className="ticket-list__item"
                  onClick={() => handleTicketClick(ticket)}
                >
                  <div className="ticket-list__item-header">
                    <h3 className="ticket-list__item-subject">{ticket.subject}</h3>
                    <div className="ticket-list__item-badges">
                      {renderPriorityBadge(ticket.priority)}
                      {renderStatusBadge(ticket.status)}
                    </div>
                  </div>
                  
                  <div className="ticket-list__item-meta">
                    <span className="ticket-list__item-id">#{ticket.id.slice(-8)}</span>
                    <span className="ticket-list__item-date">
                      {supportService.getRelativeTime(ticket.createdAt)}
                    </span>
                  </div>
                  
                  {ticket.description && (
                    <div className="ticket-list__item-preview">
                      {ticket.description.replace(/<[^>]*>/g, '').substring(0, 150)}
                      {ticket.description.length > 150 ? '...' : ''}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {renderPagination()}
        </>
      )}

      {/* Ticket Details Modal */}
      {showTicketModal && selectedTicket && (
        <Modal
          isOpen={showTicketModal}
          onClose={handleCloseTicketModal}
          title={`Ticket #${selectedTicket.id.slice(-8)}`}
          size="large"
        >
          <TicketDetails
            ticket={selectedTicket}
            onClose={handleCloseTicketModal}
          />
        </Modal>
      )}
    </div>
  );
};

export default TicketList;
