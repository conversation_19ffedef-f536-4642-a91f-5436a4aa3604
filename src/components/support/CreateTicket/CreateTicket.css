/* Create Ticket Component */
.create-ticket {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

/* Guidelines Section */
.create-ticket__guidelines {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border-left: 4px solid var(--accent-color);
}

.create-ticket__guidelines-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.create-ticket__guidelines-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.create-ticket__guidelines-close {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.create-ticket__guidelines-close:hover {
  color: var(--text-primary);
  background: var(--hover-bg);
}

.create-ticket__guidelines-list {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
  line-height: 1.6;
}

.create-ticket__guidelines-list li {
  margin-bottom: 8px;
}

/* Form Styling */
.create-ticket__form {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
}

.create-ticket__form-group {
  margin-bottom: 24px;
}

.create-ticket__label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
}

.create-ticket__label-optional {
  color: var(--text-muted);
  font-weight: 400;
  margin-left: 4px;
}

/* Input Styling */
.create-ticket__input,
.create-ticket__select,
.create-ticket__textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--secondary-bg);
  color: var(--text-primary);
  font-size: 14px;
  font-family: inherit;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.create-ticket__input:focus,
.create-ticket__select:focus,
.create-ticket__textarea:focus {
  outline: none;
  border-color: var(--accent-color);
  background: var(--card-bg);
}

.create-ticket__input--error,
.create-ticket__select--error,
.create-ticket__textarea--error {
  border-color: #ef4444;
}

.create-ticket__textarea {
  resize: vertical;
  min-height: 100px;
  line-height: 1.5;
}

/* File Upload Styling */
.create-ticket__file-upload {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s ease;
}

.create-ticket__file-upload:hover {
  border-color: var(--accent-color);
  background: rgba(252, 212, 105, 0.05);
}

.create-ticket__file-input {
  display: none;
}

.create-ticket__file-label {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: var(--accent-color);
  color: var(--primary-bg);
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  font-size: 14px;
}

.create-ticket__file-label:hover {
  background: #e6b800;
  transform: translateY(-1px);
}

.create-ticket__file-hint {
  display: block;
  margin-top: 8px;
  color: var(--text-muted);
  font-size: 12px;
}

.create-ticket__file-selected {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--secondary-bg);
  border: 2px solid var(--border-color);
  border-radius: 8px;
}

.create-ticket__file-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.create-ticket__file-name {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
}

.create-ticket__file-size {
  color: var(--text-muted);
  font-size: 12px;
}

.create-ticket__file-remove {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.create-ticket__file-remove:hover {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

/* Error Styling */
.create-ticket__error {
  display: block;
  margin-top: 6px;
  color: #ef4444;
  font-size: 12px;
  font-weight: 500;
}

.create-ticket__submit-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #ef4444;
  border-radius: 6px;
  padding: 12px 16px;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
}

/* Actions */
.create-ticket__actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
}

.create-ticket__submit {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 14px 28px;
  background: var(--accent-color);
  color: var(--primary-bg);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 180px;
  justify-content: center;
}

.create-ticket__submit:hover:not(:disabled) {
  background: #e6b800;
  transform: translateY(-1px);
}

.create-ticket__submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.create-ticket__spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .create-ticket {
    padding: 16px;
  }

  .create-ticket__form {
    padding: 20px;
  }

  .create-ticket__guidelines {
    padding: 16px;
  }

  .create-ticket__file-upload {
    padding: 20px 16px;
  }

  .create-ticket__file-selected {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .create-ticket__actions {
    justify-content: stretch;
  }

  .create-ticket__submit {
    width: 100%;
  }
}
