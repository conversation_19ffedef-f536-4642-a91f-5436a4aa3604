import React, { useState, useEffect } from 'react';
import supportService from '../../../services/supportService';
import RichTextEditor from '../RichTextEditor/RichTextEditor';
import './CreateTicket.css';

const CreateTicket = ({ onTicketCreated }) => {
  const [formData, setFormData] = useState({
    subject: '',
    description: '',
    priority: 'MEDIUM',
    techDetails: ''
  });
  const [priorities, setPriorities] = useState([]);
  const [supportInfo, setSupportInfo] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});
  const [showGuidelines, setShowGuidelines] = useState(true);

  const loadInitialData = async () => {
    try {
      const [prioritiesResponse, infoResponse] = await Promise.all([
        supportService.getPriorities(),
        supportService.getSupportInfo()
      ]);

      if (prioritiesResponse.success) {
        setPriorities(prioritiesResponse.data.priorities);
      }

      if (infoResponse.success) {
        setSupportInfo(infoResponse.data);
      }
    } catch (error) {
      console.error('Error loading support data:', error);
    }
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const handleDescriptionChange = (content) => {
    setFormData(prev => ({
      ...prev,
      description: content
    }));
    
    if (errors.description) {
      setErrors(prev => ({
        ...prev,
        description: null
      }));
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Clear previous errors
    setErrors(prev => ({
      ...prev,
      attachment: null
    }));

    // Validate file size (max 5MB as per new API)
    if (file.size > 5 * 1024 * 1024) {
      setErrors(prev => ({
        ...prev,
        attachment: 'File size must be less than 5MB'
      }));
      return;
    }

    // Validate file type (updated to match new API requirements)
    const allowedTypes = [
      // Images
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      // Documents
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword', // .doc
      'text/plain',
      // Archives
      'application/zip'
    ];

    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({
        ...prev,
        attachment: 'Only images (JPEG, PNG, GIF, WebP), documents (PDF, DOC, DOCX, TXT), and ZIP files are allowed'
      }));
      return;
    }

    setSelectedFile(file);
    setErrors(prev => ({
      ...prev,
      attachment: null
    }));
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    setErrors(prev => ({
      ...prev,
      attachment: null
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    } else if (formData.subject.length < 5) {
      newErrors.subject = 'Subject must be at least 5 characters';
    }

    if (!formData.description.trim() || formData.description === '<p><br></p>') {
      newErrors.description = 'Description is required';
    }

    if (!formData.priority) {
      newErrors.priority = 'Priority is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      const ticketData = {
        subject: formData.subject.trim(),
        description: formData.description,
        priority: formData.priority,
        ...(formData.techDetails.trim() && { techDetails: formData.techDetails.trim() })
      };

      // Pass the file separately to the service
      const response = await supportService.createTicket(ticketData, selectedFile);

      if (response.success) {
        // Reset form
        setFormData({
          subject: '',
          description: '',
          priority: 'MEDIUM',
          techDetails: ''
        });
        setSelectedFile(null);
        setErrors({});

        // Notify parent component
        if (onTicketCreated) {
          onTicketCreated(response.data.ticket);
        }
      }
    } catch (error) {
      setErrors(prev => ({
        ...prev,
        submit: error.message
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="create-ticket">
      {showGuidelines && supportInfo && (
        <div className="create-ticket__guidelines">
          <div className="create-ticket__guidelines-header">
            <h3>📋 Support Guidelines</h3>
            <button
              type="button"
              onClick={() => setShowGuidelines(false)}
              className="create-ticket__guidelines-close"
            >
              ✕
            </button>
          </div>
          <ul className="create-ticket__guidelines-list">
            {supportInfo.guidelines.items.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </div>
      )}

      <form onSubmit={handleSubmit} className="create-ticket__form">
        <div className="create-ticket__form-group">
          <label htmlFor="subject" className="create-ticket__label">
            Subject *
          </label>
          <input
            type="text"
            id="subject"
            name="subject"
            value={formData.subject}
            onChange={handleInputChange}
            placeholder="Brief description of your issue"
            className={`create-ticket__input ${errors.subject ? 'create-ticket__input--error' : ''}`}
            maxLength={200}
            disabled={isSubmitting}
          />
          {errors.subject && (
            <span className="create-ticket__error">{errors.subject}</span>
          )}
        </div>

        <div className="create-ticket__form-group">
          <label htmlFor="priority" className="create-ticket__label">
            Priority *
          </label>
          <select
            id="priority"
            name="priority"
            value={formData.priority}
            onChange={handleInputChange}
            className={`create-ticket__select ${errors.priority ? 'create-ticket__select--error' : ''}`}
            disabled={isSubmitting}
          >
            {priorities.map(priority => (
              <option key={priority.value} value={priority.value}>
                {priority.label} - {priority.description}
              </option>
            ))}
          </select>
          {errors.priority && (
            <span className="create-ticket__error">{errors.priority}</span>
          )}
        </div>

        <div className="create-ticket__form-group">
          <label className="create-ticket__label">
            Description *
          </label>
          <RichTextEditor
            value={formData.description}
            onChange={handleDescriptionChange}
            placeholder="Provide detailed information about your issue..."
            error={errors.description}
            maxLength={5000}
            disabled={isSubmitting}
          />
        </div>

        <div className="create-ticket__form-group">
          <label htmlFor="techDetails" className="create-ticket__label">
            Technical Details
            <span className="create-ticket__label-optional">(Optional)</span>
          </label>
          <textarea
            id="techDetails"
            name="techDetails"
            value={formData.techDetails}
            onChange={handleInputChange}
            placeholder="Browser, OS, error messages, steps to reproduce..."
            className="create-ticket__textarea"
            rows={4}
            maxLength={1000}
            disabled={isSubmitting}
          />
        </div>

        <div className="create-ticket__form-group">
          <label className="create-ticket__label">
            Attachment
            <span className="create-ticket__label-optional">(Optional)</span>
          </label>
          
          {!selectedFile ? (
            <div className="create-ticket__file-upload">
              <input
                type="file"
                id="attachment"
                onChange={handleFileSelect}
                accept="image/*,.pdf,.txt,.docx,.doc,.zip"
                className="create-ticket__file-input"
                disabled={isSubmitting}
              />
              <label htmlFor="attachment" className="create-ticket__file-label">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
                </svg>
                Choose file
              </label>
              <span className="create-ticket__file-hint">
                Images, documents (PDF, DOC, DOCX, TXT), or ZIP files (max 5MB)
              </span>
            </div>
          ) : (
            <div className="create-ticket__file-selected">
              <div className="create-ticket__file-info">
                <span className="create-ticket__file-name">{selectedFile.name}</span>
                <span className="create-ticket__file-size">
                  ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                </span>
              </div>
              <button
                type="button"
                onClick={handleRemoveFile}
                className="create-ticket__file-remove"
                disabled={isSubmitting}
              >
                ✕
              </button>
            </div>
          )}
          
          {errors.attachment && (
            <span className="create-ticket__error">{errors.attachment}</span>
          )}
        </div>

        {errors.submit && (
          <div className="create-ticket__submit-error">
            {errors.submit}
          </div>
        )}

        <div className="create-ticket__actions">
          <button
            type="submit"
            className="create-ticket__submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="create-ticket__spinner"></div>
                Creating Ticket...
              </>
            ) : (
              'Create Support Ticket'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateTicket;
