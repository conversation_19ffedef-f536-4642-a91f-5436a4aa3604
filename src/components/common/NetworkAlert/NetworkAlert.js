import React, { useState, useEffect } from 'react';
import './NetworkAlert.css';

const NetworkAlert = () => {
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState('offline'); // 'offline' or 'online'

  useEffect(() => {
    const handleOnline = () => {
      setAlertType('online');
      setShowAlert(true);

      // Auto-hide the "back online" alert after 3 seconds
      setTimeout(() => {
        setShowAlert(false);
      }, 3000);
    };

    const handleOffline = () => {
      setAlertType('offline');
      setShowAlert(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Show alert immediately if offline
    if (!navigator.onLine) {
      setAlertType('offline');
      setShowAlert(true);
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleDismiss = () => {
    setShowAlert(false);
  };

  if (!showAlert) {
    return null;
  }

  const isOfflineAlert = alertType === 'offline';

  return (
    <div className={`network-alert ${isOfflineAlert ? 'network-alert--offline' : 'network-alert--online'}`}>
      <div className="network-alert__content">
        <div className="network-alert__icon">
          {isOfflineAlert ? (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
              <line x1="12" y1="9" x2="12" y2="13"/>
              <line x1="12" y1="17" x2="12.01" y2="17"/>
            </svg>
          ) : (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
              <polyline points="22,4 12,14.01 9,11.01"/>
            </svg>
          )}
        </div>
        <div className="network-alert__message">
          <strong>{isOfflineAlert ? 'No Internet Connection' : 'Back Online'}</strong>
          <p>{isOfflineAlert ? 'Please check your connection and try again.' : 'Your internet connection has been restored.'}</p>
        </div>
        <button
          className="network-alert__dismiss"
          onClick={handleDismiss}
          aria-label="Dismiss alert"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>
    </div>
  );
};

export default NetworkAlert;
