.network-alert {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1200;
  max-width: 400px;
  animation: slideInRight 0.3s ease-out;
}

.network-alert__content {
  color: white;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: flex-start;
  gap: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.network-alert--offline .network-alert__content {
  background: #ef4444;
}

.network-alert--online .network-alert__content {
  background: #10b981;
}

.network-alert__icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.network-alert__icon svg {
  color: white;
}

.network-alert__message {
  flex: 1;
}

.network-alert__message strong {
  display: block;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
}

.network-alert__message p {
  margin: 0;
  font-size: 13px;
  opacity: 0.9;
  line-height: 1.4;
}

.network-alert__dismiss {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
  opacity: 0.8;
}

.network-alert__dismiss:hover {
  background: rgba(255, 255, 255, 0.1);
  opacity: 1;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .network-alert {
    top: 16px;
    right: 16px;
    left: 16px;
    max-width: none;
  }

  .network-alert__content {
    padding: 14px 16px;
  }

  .network-alert__message strong {
    font-size: 13px;
  }

  .network-alert__message p {
    font-size: 12px;
  }
}
