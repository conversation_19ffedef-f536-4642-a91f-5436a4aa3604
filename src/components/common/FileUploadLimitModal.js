import React from 'react';
import { useNavigate } from 'react-router-dom';
import Modal from './Modal';
import './FileUploadLimitModal.css';

const FileUploadLimitModal = ({ isOpen, onClose, message }) => {
  const navigate = useNavigate();

  const handleUpgradeClick = () => {
    onClose();
    navigate('/pricing-billing');
  };

  const handleCloseClick = () => {
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCloseClick}
      title="File Upload Limit Reached"
      size="small"
      className="file-upload-limit-modal"
      closeOnOverlayClick={false}
      showCloseButton={false}
    >
      <div className="file-upload-limit-modal__content">
        <div className="file-upload-limit-modal__icon">
          📁
        </div>
        <p className="file-upload-limit-modal__message">
          {message || "Daily file upload limit reached."}
        </p>
        <p className="file-upload-limit-modal__description">
          Need more file uploads? Recharge an add-on or upgrade your plan below.
        </p>
        <div className="file-upload-limit-modal__actions">
          <button
            type="button"
            onClick={handleUpgradeClick}
            className="modal-btn modal-btn--primary"
          >
            Upgrade Plan
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default FileUploadLimitModal;
