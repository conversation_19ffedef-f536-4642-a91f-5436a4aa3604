import React from 'react';
import { useNavigate } from 'react-router-dom';
import Modal from './Modal';
import './SignupModal.css';

const SignupModal = ({ isOpen, onClose, message }) => {
  const navigate = useNavigate();

  const handleSignupClick = () => {
    onClose();
    navigate('/signup');
  };

  const handleCloseClick = () => {
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCloseClick}
      title="Unlimited Access Available"
      size="small"
      className="signup-modal"
      closeOnOverlayClick={false}
      showCloseButton={false}
    >
      <div className="signup-modal__content">
        <div className="signup-modal__icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"/>
            <path d="M12 15l-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"/>
            <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"/>
            <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"/>
          </svg>
        </div>
        <p className="signup-modal__message">
          {message || "Please sign up for unlimited access!"}
        </p>
        <p className="signup-modal__description">
          Join thousands of users who are already enjoying unlimited AI conversations and advanced features.
        </p>
        <div className="signup-modal__actions">

          <button
            type="button"
            onClick={handleSignupClick}
            className="modal-btn modal-btn--primary"
          >
            Sign Up Now
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default SignupModal;
