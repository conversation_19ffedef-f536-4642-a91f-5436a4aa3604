import React from 'react';
import { Helmet } from 'react-helmet-async';

const SEO = ({
  title,
  description,
  keywords,
  canonical,
  ogTitle,
  ogDescription,
  ogImage,
  ogType = 'website',
  twitterCard = 'summary_large_image',
  noindex = false,
  nofollow = false,
  structuredData
}) => {
  const baseUrl = 'https://theinfiniai.live';
  const defaultImage = `${baseUrl}/assets/images/infini-logo.svg`;
  
  const fullTitle = title ? `${title} | the infini ai` : 'the infini ai - ChatGPT Alternative, Claude Alternative, All AI Models in One Platform';
  const metaDescription = description || 'Access ChatGPT, Claude, Gemini, and Llama AI models in one subscription. The ultimate ChatGPT alternative and Claude alternative for all your AI chat needs.';
  const metaKeywords = keywords || 'ChatGPT alternative, Claude alternative, Gemini alternative, Llama AI, AI chat, multi AI platform, AI subscription, GPT alternative, artificial intelligence, AI models, chat AI, AI assistant';
  
  const robotsContent = noindex || nofollow 
    ? `${noindex ? 'noindex' : 'index'},${nofollow ? 'nofollow' : 'follow'}`
    : 'index,follow';

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={metaDescription} />
      <meta name="keywords" content={metaKeywords} />
      <meta name="robots" content={robotsContent} />
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={`${baseUrl}${canonical}`} />}
      
      {/* Open Graph Tags */}
      <meta property="og:title" content={ogTitle || fullTitle} />
      <meta property="og:description" content={ogDescription || metaDescription} />
      <meta property="og:image" content={ogImage || defaultImage} />
      <meta property="og:type" content={ogType} />
      <meta property="og:site_name" content="the infini ai" />
      {canonical && <meta property="og:url" content={`${baseUrl}${canonical}`} />}
      
      {/* Twitter Card Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={ogTitle || fullTitle} />
      <meta name="twitter:description" content={ogDescription || metaDescription} />
      <meta name="twitter:image" content={ogImage || defaultImage} />
      
      {/* Additional Meta Tags */}
      <meta name="author" content="the infini ai" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#0a0a0a" />
      
      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEO;
