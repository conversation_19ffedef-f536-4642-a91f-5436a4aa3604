.file-upload-limit-modal .modal__content {
  text-align: center;
  padding: 1.5rem;
}

.file-upload-limit-modal__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.file-upload-limit-modal__icon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.file-upload-limit-modal__message {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary, #1a1a1a);
  margin: 0;
  line-height: 1.4;
}

.file-upload-limit-modal__description {
  font-size: 0.95rem;
  color: var(--text-secondary, #666);
  margin: 0;
  line-height: 1.5;
  max-width: 300px;
}

.file-upload-limit-modal__actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.file-upload-limit-modal__actions .modal-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  max-width: 200px;
}

.modal-btn--primary {
  background-color: var(--accent-color);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.modal-btn--primary:hover {
  background-color: var(--primary-hover, #0056b3);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.modal-btn--primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

/* Responsive design */
@media (max-width: 480px) {
  .file-upload-limit-modal__actions {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .file-upload-limit-modal__actions .modal-btn {
    max-width: none;
    width: 100%;
  }
  
  .file-upload-limit-modal__description {
    font-size: 0.9rem;
  }
}
