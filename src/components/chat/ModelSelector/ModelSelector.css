.model-selector {
  position: relative;
  display: inline-block;
}

.model-selector--disabled {
  opacity: 0.6;
  pointer-events: none;
}

.model-selector__trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 10px 14px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 200px;
  font-size: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.model-selector__trigger:hover:not(:disabled) {
  background: var(--hover-bg);
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.model-selector__trigger--open {
  border-color: var(--primary-color);
  background: var(--hover-bg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.model-selector__trigger:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.model-selector__current {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.model-selector__logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.model-selector__logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.model-selector__icon {
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.model-selector__info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.model-selector__name {
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1;
}

.model-selector__provider {
  font-size: 11px;
  color: var(--text-muted);
  line-height: 1;
}

.model-selector__arrow {
  color: var(--text-muted);
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

.model-selector__arrow--up {
  transform: rotate(180deg);
}

.model-selector__dropdown {
  position: absolute;
  left: auto;
  right: 0;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  max-height: 450px;
  overflow-y: auto;
  min-width: 350px;
  backdrop-filter: blur(10px);
}

.model-selector__dropdown--top {
  bottom: 100%;
  margin-bottom: 4px;
}

.model-selector__dropdown--bottom {
  top: -410px;
  margin-top: 4px;
}

.model-selector__header {
  padding: 20px 20px 16px 20px;
  border-bottom: 1px solid var(--border-color);
}

.model-selector__header h4 {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.model-selector__header p {
  margin: 0;
  font-size: 13px;
  color: var(--text-muted);
  line-height: 1.4;
}

.model-selector__options {
  padding: 12px;
}

.model-selector__option {
  display: block;
  width: 100%;
  background: none;
  border: none;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  position: relative;
  margin-bottom: 6px;
  border: 1px solid transparent;
}

.model-selector__option:last-child {
  margin-bottom: 0;
}

.model-selector__option:hover {
  background: var(--hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.model-selector__option--selected {
  background: var(--primary-color-alpha);
  border: 1px solid var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.model-selector__option-main {
  display: flex;
  align-items: center;
  gap: 14px;
  margin-bottom: 8px;
}

.model-selector__option-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  flex-shrink: 0;
}

.model-selector__option-logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 6px;
}

.model-selector__option-icon {
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.model-selector__option-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.model-selector__option-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 15px;
  line-height: 1.3;
}

.model-selector__option-provider {
  font-size: 12px;
  color: var(--text-muted);
  line-height: 1.2;
  font-weight: 500;
}

.model-selector__option-badges {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
}

.model-selector__tier {
  padding: 3px 8px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.model-selector__multimodal-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 9px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #4caf50;
}

.model-selector__tier--basic {
  background: #e3f2fd;
  color: #1976d2;
}

.model-selector__tier--advanced {
  background: #f3e5f5;
  color: #7b1fa2;
}

.model-selector__tier--premium {
  background: #fff3e0;
  color: #f57c00;
}

.model-selector__option-description {
  font-size: 13px;
  color: var(--text-muted);
  line-height: 1.4;
  margin-left: 42px;
  margin-top: 2px;
}

.model-selector__option-check {
  position: absolute;
  top: 12px;
  right: 12px;
  color: var(--primary-color);
}

.model-selector__empty {
  padding: 20px;
  text-align: center;
}

.model-selector__empty p {
  margin: 0;
  color: var(--text-muted);
  font-size: 14px;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .model-selector__tier--basic {
    background: rgba(25, 118, 210, 0.2);
    color: #64b5f6;
  }

  .model-selector__tier--advanced {
    background: rgba(123, 31, 162, 0.2);
    color: #ba68c8;
  }

  .model-selector__tier--premium {
    background: rgba(245, 124, 0, 0.2);
    color: #ffb74d;
  }

  .model-selector__multimodal-badge {
    background: rgba(76, 175, 80, 0.2);
    color: #81c784;
    border: 1px solid #4caf50;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .model-selector__dropdown {
    min-width: 320px;
    max-height: 350px;
  }

  .model-selector__trigger {
    min-width: 180px;
  }

  .model-selector__dropdown--bottom{
    top: -360px;
  }

  .model-selector__option-description {
    margin-left: 38px;
  }
}
