import React, { useState, forwardRef, useImperativeHandle, useRef } from 'react';
import ModelSelector from '../ModelSelector/ModelSelector';
import FileAttachment from '../FileAttachment/FileAttachment';
import WebSearchInput from '../WebSearchInput/WebSearchInput';
import AppsInput from '../AppsInput/AppsInput';
import AppsModal from '../AppsModal/AppsModal';
import { useChat } from '../../../contexts/ChatContext';
import modelService from '../../../services/modelService';
import './ChatInput.css';

const ChatInput = forwardRef(({ onSendMessage, onSendMessageWithFile, onSendWebSearch, onSendWebSearchWithFile, disabled = false, isLoading = false }, ref) => {
  const [message, setMessage] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [isWebSearchMode, setIsWebSearchMode] = useState(false);
  const [isAppsModalOpen, setIsAppsModalOpen] = useState(false);
  const fileAttachmentRef = useRef(null);
  const maxLength = 3000;
  const { availableModels, selectedModel } = useChat();

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    handleFileSelect: (file) => {
      setSelectedFile(file);
      // Also trigger the FileAttachment component's file selection
      if (fileAttachmentRef.current && fileAttachmentRef.current.handleExternalFileSelect) {
        fileAttachmentRef.current.handleExternalFileSelect(file);
      }
    }
  }));

  const handleSubmit = (e) => {
    e.preventDefault();
    // Allow sending if there's either a message or a file attachment
    const hasContent = (message.trim() && message.length <= maxLength) || selectedFile;

    if (hasContent && !disabled && !isLoading) {
      if (isWebSearchMode) {
        // Web search mode
        if (selectedFile && onSendWebSearchWithFile) {
          // Web search with file attachment
          onSendWebSearchWithFile(message.trim() || '', selectedFile);
        } else if (message.trim() && onSendWebSearch) {
          // Web search text-only
          onSendWebSearch(message.trim());
        }
      } else {
        // Regular chat mode
        if (selectedFile && onSendMessageWithFile) {
          // Send with file attachment (message can be empty)
          onSendMessageWithFile(message.trim() || '', selectedFile);
        } else if (message.trim()) {
          // Send text-only message
          onSendMessage(message.trim());
        }
      }
      setMessage('');
      setSelectedFile(null);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleTextareaChange = (e) => {
    setMessage(e.target.value);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  };

  const handleFileSelect = (file) => {
    setSelectedFile(file);
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
  };

  const handleWebSearchToggle = (enabled) => {
    setIsWebSearchMode(enabled);
  };

  const handleAppsToggle = () => {
    setIsAppsModalOpen(true);
  };

  const handleAppsModalClose = () => {
    setIsAppsModalOpen(false);
  };

  const handleModelSelect = (modelId) => {
    // Model selection handled by parent component
  };

  // Check if the selected model supports multimodal capabilities
  const getSelectedModelInfo = () => {
    if (!selectedModel || !availableModels) return null;

    const modelObj = availableModels.find(model =>
      (typeof model === 'object' ? model.modelId : model) === selectedModel
    );

    return modelObj;
  };

  const selectedModelInfo = getSelectedModelInfo();
  const isFileAttachmentEnabled = selectedModelInfo ? modelService.isMultimodal(selectedModelInfo) : false;

  return (
    <div className="chat-input">
      <form onSubmit={handleSubmit} className="chat-input__form">
        <div className="chat-input__container">
          <div className="chat-input__field-wrapper">
            <textarea
              value={message}
              onChange={handleTextareaChange}
              onKeyPress={handleKeyPress}
              placeholder={isLoading ? "Generating response..." : isWebSearchMode ? "Search the web..." : "What do you want to know?"}
              className="chat-input__field"
              rows="1"
              maxLength={maxLength}
              disabled={disabled || isLoading}
            />
          </div>

          <button
            type="submit"
            className="chat-input__send"
            disabled={(!message.trim() && !selectedFile) || message.length > maxLength || disabled || isLoading}
          >
            {isLoading ? (
              <div className="chat-input__loading-spinner"></div>
            ) : (
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <path d="M22 2L11 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            )}
          </button>
        </div>

        <div className="chat-input__actions">
          <FileAttachment
            ref={fileAttachmentRef}
            onFileSelect={handleFileSelect}
            onFileRemove={handleFileRemove}
            disabled={disabled || isLoading || (!isFileAttachmentEnabled && !isWebSearchMode)}
          />

          <WebSearchInput
            onWebSearchToggle={handleWebSearchToggle}
            disabled={disabled || isLoading}
            isWebSearchMode={isWebSearchMode}
          />

          <AppsInput
            onAppsToggle={handleAppsToggle}
            disabled={disabled || isLoading}
          />

          <div className="chat-input__model-selector">
            <ModelSelector
              onModelSelect={handleModelSelect}
              disabled={disabled || isLoading}
            />
          </div>
        </div>

        {/* Show a hint when file attachment is disabled */}
        {!isFileAttachmentEnabled && !isWebSearchMode && selectedModelInfo && (
          <div className="chat-input__file-hint">
            <span className="chat-input__file-hint-text">
              File attachments are not supported by {modelService.formatModelName(selectedModelInfo)}.
              Select a multimodal model to enable file uploads.
            </span>
          </div>
        )}

        {/* Show web search mode indicator */}
        {isWebSearchMode && (
          <div className="chat-input__web-search-hint">
            <span className="chat-input__web-search-hint-text">
              Web search mode enabled. Your query will search the internet for real-time information.
            </span>
          </div>
        )}
      </form>

      {/* AI Disclaimer */}
      <div className="chat-input__disclaimer">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="8" x2="12" y2="12"/>
          <line x1="12" y1="16" x2="12.01" y2="16"/>
        </svg>
        <span>AI responses might have errors. Please double-check important information.</span>
      </div>

      {/* Apps Modal */}
      <AppsModal
        isOpen={isAppsModalOpen}
        onClose={handleAppsModalClose}
      />
    </div>
  );
});

export default ChatInput;
