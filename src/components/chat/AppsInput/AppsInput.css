.apps-input {
  position: relative;
}

/* Apps Button */
.apps-input__button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.apps-input__button:hover:not(:disabled) {
  background: var(--card-bg);
  color: var(--text-primary);
}

.apps-input__button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.apps-input__button svg {
  flex-shrink: 0;
}

.apps-input__label {
  font-size: 12px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .apps-input__button {
    padding: 6px 10px;
    font-size: 13px;
  }

  .apps-input__label {
    font-size: 11px;
  }
}
