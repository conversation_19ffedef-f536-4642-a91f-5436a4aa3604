import React from 'react';
import './AppsInput.css';

const AppsInput = ({ onAppsToggle, disabled = false }) => {
  const handleToggle = () => {
    if (disabled) return;
    onAppsToggle();
  };

  return (
    <div className="apps-input">
      <button
        type="button"
        className="apps-input__button"
        onClick={handleToggle}
        disabled={disabled}
        title="Open Apps"
      >
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
          <rect x="3" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
          <rect x="14" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
          <rect x="3" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
          <rect x="14" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
        </svg>
        <span className="apps-input__label">Apps</span>
      </button>
    </div>
  );
};

export default AppsInput;
