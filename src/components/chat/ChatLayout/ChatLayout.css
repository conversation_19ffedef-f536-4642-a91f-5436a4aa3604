.chat-layout {
  display: flex;
  height: 100vh;
  background-color: var(--primary-bg);
  color: var(--text-primary);
  position: relative;
}

/* Mobile overlay */
.chat-layout__overlay {
  display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-layout {
    flex-direction: row;
    position: fixed;
    height: 100vh;
    width: 100%;
  }

  .chat-layout__overlay {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    backdrop-filter: blur(2px);
  }
}
