import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import { useChat } from '../../../contexts/ChatContext';
import { ConfirmationModal } from '../../common';

import './Sidebar.css';

const Sidebar = ({ isOpen, onToggle, onCreateProject }) => {
  const [activeItem, setActiveItem] = useState('new-chat');
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState({
    isOpen: false,
    threadId: null,
    threadName: '',
    isDeleting: false
  });
  const { user, logout } = useAuth();
  const {
    threads,
    projects,
    currentThread,
    currentProject,
    projectThreads,
    expandedProjects,
    isLoading,
    startNewChat,
    startNewProjectChat,
    selectThread,
    toggleProjectExpansion,
    searchProjects,
    searchResults,
    deleteThread
  } = useChat();
  const navigate = useNavigate();

  const topMenuItems = [
    {
      id: 'search',
      label: 'Search',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
      )
    }
  ];

  const bottomItems = [
    {
      id: 'settings',
      label: 'Settings',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="12" cy="12" r="3"/>
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-3.5L19 4m-7 7l-2.5 2.5M7.5 4.5L5 7m7 7l2.5 2.5"/>
        </svg>
      )
    }
  ];

  // Note: Data loading is handled by ChatContext, no need to duplicate here

  const handleItemClick = (itemId) => {
    setActiveItem(itemId);

    if (itemId === 'new-chat') {
      startNewChat();
      navigate('/chat');
    } else if (itemId === 'search') {
      setShowSearch(!showSearch);
    } else if (itemId === 'settings') {
      navigate('/profile');
    }
  };

  const handleNewChat = () => {
    startNewChat();
    setActiveItem('new-chat');
    // Navigate to base chat URL for new chat
    navigate('/chat');
  };

  const handleCreateProject = () => {
    onCreateProject();
  };

  const handleThreadClick = (thread) => {
    selectThread(thread);
    setActiveItem('threads');
    // Navigate to the thread-specific URL
    navigate(`/chat/${thread.id}`);
  };

  const handleProjectClick = (project) => {
    toggleProjectExpansion(project.id);
    setActiveItem('projects');
  };

  const handleNewProjectChat = (project, e) => {
    e.stopPropagation(); // Prevent project expansion/collapse
    startNewProjectChat(project);
    setActiveItem('projects');
    // Navigate to base chat URL for new project chat
    navigate('/chat');
  };

  const handleDeleteThread = (threadId, threadName, e) => {
    e.stopPropagation(); // Prevent thread selection when clicking delete
    setDeleteConfirmation({
      isOpen: true,
      threadId,
      threadName,
      isDeleting: false
    });
  };

  const confirmDeleteThread = async () => {
    setDeleteConfirmation(prev => ({ ...prev, isDeleting: true }));

    try {
      await deleteThread(deleteConfirmation.threadId);
      setDeleteConfirmation({
        isOpen: false,
        threadId: null,
        threadName: '',
        isDeleting: false
      });
    } catch (error) {
      console.error('Failed to delete thread:', error);
      // Keep modal open on error so user can try again, but stop loading
      setDeleteConfirmation(prev => ({ ...prev, isDeleting: false }));
    }
  };

  const cancelDeleteThread = () => {
    setDeleteConfirmation({
      isOpen: false,
      threadId: null,
      threadName: '',
      isDeleting: false
    });
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      searchProjects(searchQuery.trim());
    }
  };

  const handleSearchInputChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleLogoClick = () => {
    navigate('/');
  };

  const getUserInitials = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
    } else if (user?.email) {
      return user.email[0].toUpperCase();
    }
    return 'G';
  };

  const getUserName = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName} ${user.lastName}`;
    } else if (user?.firstName) {
      return user.firstName;
    } else if (user?.email) {
      return user.email.split('@')[0];
    }
    return 'Guest';
  };

  const getUserPlan = () => {
    return user?.plan || 'Free Plan';
  };

  return (
    <div className={`sidebar ${isOpen ? 'sidebar--open' : 'sidebar--closed'}`}>
      {/* Header */}
      <div className="sidebar__header">
        <div className="sidebar__logo" onClick={handleLogoClick}>
          <img
            src="/assets/images/infini-logo.svg"
            alt="the infini ai"
            className="sidebar__logo-img"
          />
        </div>
        <button className="sidebar__toggle" onClick={onToggle}>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <rect x="3" y="4" width="18" height="16" rx="2" stroke="currentColor" strokeWidth="2" fill="none"/>
            <line x1="9" y1="4" x2="9" y2="20" stroke="currentColor" strokeWidth="2"/>
          </svg>
        </button>
      </div>

      {/* Content */}
      <div className="sidebar__content">



      {/* Top Menu Items */}
      {isOpen && (
        <nav className="sidebar__top-nav">
          {/* New Chat Button */}
          <div className="sidebar__new-chat">
            <button className="sidebar__new-chat-btn" onClick={handleNewChat}>
              <span className="sidebar__new-chat-icon">+</span>
              {isOpen && <span>New chat</span>}
            </button>
          </div>
          <ul className="sidebar__nav-list">
            {topMenuItems.map((item) => (
              <li key={item.id}>
                <button
                  className={`sidebar__nav-item ${activeItem === item.id ? 'sidebar__nav-item--active' : ''}`}
                  onClick={() => handleItemClick(item.id)}
                >
                  <span className="sidebar__nav-icon">{item.icon}</span>
                  <span className="sidebar__nav-label">{item.label}</span>
                </button>
              </li>
            ))}
          </ul>
        </nav>
      )}

      {/* Search Section */}
      {showSearch && isOpen && (
        <div className="sidebar__search">
          <form onSubmit={handleSearch} className="sidebar__search-form">
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchInputChange}
              placeholder="Search projects..."
              className="sidebar__search-input"
            />
            <button type="submit" className="sidebar__search-btn">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2"/>
                <path d="m21 21-4.35-4.35" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              </svg>
            </button>
          </form>

          {searchResults.length > 0 && (
            <div className="sidebar__search-results">
              <div className="sidebar__section-title">Search Results</div>
              {searchResults.map(project => (
                <div
                  key={project.id}
                  className={`sidebar__item ${currentProject?.id === project.id ? 'sidebar__item--active' : ''}`}
                  onClick={() => handleProjectClick(project)}
                >
                  <span className="sidebar__item-icon">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
                    </svg>
                  </span>
                  <span className="sidebar__item-text">{project.name}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Projects Section */}
      {isOpen && (
        <div className="sidebar__section">
          <div className="sidebar__section-header">
            <span className="sidebar__section-title">Projects</span>
            <button
              className="sidebar__section-action"
              onClick={handleCreateProject}
              title="Create new project"
            >
              +
            </button>
          </div>

          {isLoading ? (
            <div className="sidebar__loading">Loading...</div>
          ) : (
            <div className="sidebar__items">
              {projects.map(project => {
                const isExpanded = expandedProjects.has(project.id);
                const threads = projectThreads[project.id] || [];

                return (
                  <div key={project.id} className="sidebar__project-container">
                    <div
                      className={`sidebar__item sidebar__project-item ${currentProject?.id === project.id ? 'sidebar__item--active' : ''}`}
                      onClick={() => handleProjectClick(project)}
                    >
                      <span className="sidebar__item-expand-icon">
                        {isExpanded ? (
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
                            <path d="M2 7h20"/>
                          </svg>
                        ) : (
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
                          </svg>
                        )}
                      </span>
                      <span className="sidebar__item-text">{project.name}</span>
                      <span className="sidebar__expand-arrow">
                        {isExpanded ? (
                          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="6,9 12,15 18,9"/>
                          </svg>
                        ) : (
                          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="9,18 15,12 9,6"/>
                          </svg>
                        )}
                      </span>
                    </div>

                    {isExpanded && (
                      <div className="sidebar__project-threads">
                        {/* New Chat Button for Project */}
                        <div className="sidebar__project-new-chat">
                          <button
                            className="sidebar__project-new-chat-btn"
                            onClick={(e) => handleNewProjectChat(project, e)}
                            title="Start new chat in this project"
                          >
                            <span className="sidebar__item-icon">+</span>
                            <span className="sidebar__item-text">New chat</span>
                          </button>
                        </div>

                        {threads.length > 0 ? (
                          threads.map(thread => (
                            <div
                              key={thread.id}
                              className={`sidebar__item sidebar__thread-item ${currentThread?.id === thread.id ? 'sidebar__item--active' : ''}`}
                              onClick={() => handleThreadClick(thread)}
                            >
                              <span className="sidebar__item-text">{thread.name}</span>
                              <button
                                className="sidebar__item-delete"
                                onClick={(e) => handleDeleteThread(thread.id, thread.name, e)}
                                title="Delete thread"
                              >
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                  <polyline points="3,6 5,6 21,6"/>
                                  <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"/>
                                  <line x1="10" y1="11" x2="10" y2="17"/>
                                  <line x1="14" y1="11" x2="14" y2="17"/>
                                </svg>
                              </button>
                            </div>
                          ))
                        ) : (
                          <div className="sidebar__empty sidebar__project-empty">No chats in this project yet</div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
              {projects.length === 0 && (
                <div className="sidebar__empty">No projects yet</div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Recent Chats Section - Only show threads not in projects */}
      {isOpen && threads.length > 0 && (
        <div className="sidebar__section">
          <div className="sidebar__section-header">
            <span className="sidebar__section-title">Recent Chats</span>
          </div>

          <div className="sidebar__items">
            {threads.filter(thread => !thread.projectId).map(thread => (
              <div
                key={thread.id}
                className={`sidebar__item ${currentThread?.id === thread.id ? 'sidebar__item--active' : ''}`}
                onClick={() => handleThreadClick(thread)}
              >
                <span className="sidebar__item-text">{thread.name}</span>
                <button
                  className="sidebar__item-delete"
                  onClick={(e) => handleDeleteThread(thread.id, thread.name, e)}
                  title="Delete thread"
                >
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polyline points="3,6 5,6 21,6"/>
                    <path d="M19,6V20a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6M8,6V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"/>
                    <line x1="10" y1="11" x2="10" y2="17"/>
                    <line x1="14" y1="11" x2="14" y2="17"/>
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      </div>

      {/* Bottom Section */}
      <div className="sidebar__bottom">
        {/* Settings */}
        {bottomItems.map((item) => (
          <button
            key={item.id}
            className={`sidebar__nav-item ${activeItem === item.id ? 'sidebar__nav-item--active' : ''}`}
            onClick={() => handleItemClick(item.id)}
          >
            <span className="sidebar__nav-icon">{item.icon}</span>
            {isOpen && <span className="sidebar__nav-label">{item.label}</span>}
          </button>
        ))}

        {/* User Profile */}
        <div className="sidebar__user-profile" onClick={() => navigate('/profile')}>
          <div className="sidebar__user-avatar">
            {user?.profilePicture ? (
              <img
                src={user.profilePicture}
                alt="Profile"
                className="sidebar__user-avatar-image"
              />
            ) : (
              <div className="sidebar__user-avatar-placeholder">
                {getUserInitials()}
              </div>
            )}
          </div>
          {isOpen && (
            <div className="sidebar__user-info">
              <div className="sidebar__user-name">{getUserName()}</div>
              <div className="sidebar__user-plan">{getUserPlan()}</div>
            </div>
          )}
          {isOpen && (
            <button
              className="sidebar__logout-btn"
              onClick={(e) => {
                e.stopPropagation();
                handleLogout();
              }}
              title="Logout"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                <polyline points="16,17 21,12 16,7"/>
                <line x1="21" y1="12" x2="9" y2="12"/>
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteConfirmation.isOpen}
        onClose={cancelDeleteThread}
        onConfirm={confirmDeleteThread}
        title="Delete Chat Thread"
        message={`Are you sure you want to delete "${deleteConfirmation.threadName}"? This action cannot be undone.`}
        confirmText="Delete Thread"
        cancelText="Cancel"
        confirmButtonType="danger"
        isLoading={deleteConfirmation.isDeleting}
      />
    </div>
  );
};

export default Sidebar;
