import React, { useState, useEffect } from 'react';
import Modal from '../../common/Modal';
import appsData from '../../../data/apps.json';
import './AppsModal.css';



const AppsModal = ({ isOpen, onClose }) => {
  const [apps, setApps] = useState([]);

  useEffect(() => {
    // Load apps from JSON data
    setApps(appsData.apps || []);
  }, []);

  const handleAppClick = (app) => {
    if (app.url) {
      window.open(app.url, '_blank', 'noopener,noreferrer');
    }
  };

  const renderAppIcon = (app) => {
    if (app.icon && (app.icon.endsWith('.png') || app.icon.endsWith('.jpg') || app.icon.endsWith('.svg'))) {
      return (
        <img
          src={app.icon}
          alt={`${app.name} logo`}
          width="48"
          height="48"
          style={{ objectFit: 'contain' }}
          onError={(e) => {
            // Replace with fallback icon if image fails to load
            e.target.outerHTML = `
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="var(--card-bg)"/>
                <circle cx="12" cy="12" r="3" fill="currentColor"/>
              </svg>
            `;
          }}
        />
      );
    }

    // Fallback default icon
    return (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
        <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2" fill="var(--card-bg)"/>
        <circle cx="12" cy="12" r="3" fill="currentColor"/>
      </svg>
    );
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Apps"
      size="custom"
      className="apps-modal"
      closeOnOverlayClick={true}
    >
      <div className="apps-modal__content">
        {apps.length > 0 ? (
          <div className="apps-modal__grid">
            {apps.map((app) => (
              <div
                key={app.id}
                className="apps-modal__app-item"
                onClick={() => handleAppClick(app)}
                title={app.description}
              >
                <div className="apps-modal__app-icon">
                  {renderAppIcon(app)}
                </div>
                <span className="apps-modal__app-name">{app.name}</span>
              </div>
            ))}
          </div>
        ) : (
          <div className="apps-modal__placeholder">
            <div className="apps-modal__placeholder-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
                <rect x="14" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
                <rect x="3" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
                <rect x="14" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
              </svg>
            </div>
            <h3 className="apps-modal__placeholder-title">Apps Coming Soon</h3>
            <p className="apps-modal__placeholder-description">
              We're working on bringing you powerful apps and integrations to enhance your chat experience.
              Stay tuned for exciting updates!
            </p>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default AppsModal;
