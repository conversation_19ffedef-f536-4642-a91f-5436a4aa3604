.web-search-input {
  position: relative;
}

/* Web Search Button */
.web-search-input__button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: var(--text-secondary, #6b7280);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.web-search-input__button:hover:not(:disabled) {
  background: var(--bg-secondary, #f9fafb);
  color: var(--text-primary, #111827);
}

.web-search-input__button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.web-search-input__button--active {
  background: var(--primary-50, #eff6ff);
  border: 1px solid var(--primary-200, #bfdbfe);
  color: var(--primary-600, #2563eb);
}

.web-search-input__button--active:hover:not(:disabled) {
  background: var(--primary-100, #dbeafe);
  border-color: var(--primary-300, #93c5fd);
  color: var(--primary-700, #1d4ed8);
}

.web-search-input__button svg {
  flex-shrink: 0;
}

.web-search-input__indicator {
  font-size: 12px;
  font-weight: 600;
}



/* Responsive Design */
@media (max-width: 768px) {
  .web-search-input__button {
    padding: 6px 10px;
    font-size: 13px;
  }

  .web-search-input__indicator {
    font-size: 11px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .web-search-input__button {
    color: #9ca3af;
  }

  .web-search-input__button:hover:not(:disabled) {
    background: #1f2937;
    color: #f9fafb;
  }

  .web-search-input__button--active {
    background: #1e3a8a;
    border-color: #3b82f6;
    color: #93c5fd;
  }

  .web-search-input__button--active:hover:not(:disabled) {
    background: #1e40af;
    border-color: #60a5fa;
    color: #bfdbfe;
  }
}
