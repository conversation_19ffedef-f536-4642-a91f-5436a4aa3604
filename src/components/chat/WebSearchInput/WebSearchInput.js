import React, { useState } from 'react';
import './WebSearchInput.css';

const WebSearchInput = ({ onWebSearchToggle, disabled = false, isWebSearchMode = false }) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleToggle = () => {
    if (disabled) return;
    onWebSearchToggle(!isWebSearchMode);
  };

  return (
    <div className="web-search-input">
      <button
        type="button"
        className={`web-search-input__button ${isWebSearchMode ? 'web-search-input__button--active' : ''}`}
        onClick={handleToggle}
        disabled={disabled}
        title={isWebSearchMode ? "Disable web search" : "Enable web search"}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
          <line x1="2" y1="12" x2="22" y2="12" stroke="currentColor" strokeWidth="2"/>
          <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" stroke="currentColor" strokeWidth="2"/>
        </svg>
        {isWebSearchMode && (
          <span className="web-search-input__indicator">
            Web Search
          </span>
        )}
      </button>
    </div>
  );
};

export default WebSearchInput;
