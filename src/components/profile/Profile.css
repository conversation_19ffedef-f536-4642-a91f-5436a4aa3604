/* Profile Components Styles */
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #1a1a1a;
  min-height: 100vh;
  color: #ffffff;
}

.profile-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #3a3a3a;
  position: relative;
}

.profile-header h1 {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #ffffff;
}

.profile-header p {
  font-size: 16px;
  color: #b0b0b0;
  margin: 0;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.profile-section {
  background: #2a2a2a;
  border-radius: 12px;
  padding: 30px;
  border: 1px solid #3a3a3a;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #3a3a3a;
  border-top: 4px solid #fcd469;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  margin-top: 16px;
  color: #b0b0b0;
  font-size: 16px;
}

/* Form Styles */
.profile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
}

.form-group input {
  background: #1a1a1a;
  border: 2px solid #3a3a3a;
  border-radius: 8px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
  width: 100%;
}

.form-group input:focus {
  outline: none;
  border-color: #fcd469;
  box-shadow: 0 0 0 3px rgba(252, 212, 105, 0.1);
}

.form-value {
  background: #1a1a1a;
  border: 2px solid #3a3a3a;
  border-radius: 8px;
  padding: 12px 16px;
  color: #b0b0b0;
  font-size: 16px;
  min-height: 20px;
}

.form-hint {
  font-size: 12px;
  color: #888888;
  margin-top: 4px;
}

/* Button Styles */
.edit-btn, .save-btn, .cancel-btn, .change-password-btn, .upload-btn, .remove-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.edit-btn, .change-password-btn {
  background: #fcd469;
  color: #1a1a1a;
}

.edit-btn:hover, .change-password-btn:hover {
  background: #f5c842;
  transform: translateY(-1px);
}

.save-btn {
  background: #10b981;
  color: #ffffff;
}

.save-btn:hover:not(:disabled) {
  background: #059669;
}

.cancel-btn, .remove-btn {
  background: transparent;
  color: #ef4444;
  border: 1px solid #ef4444;
}

.cancel-btn:hover:not(:disabled), .remove-btn:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.1);
}

.upload-btn {
  background: #fcd469;
  color: #1a1a1a;
  cursor: pointer;
  display: inline-block;
  text-align: center;
}

.upload-btn:hover {
  background: #f5c842;
}

.edit-actions {
  display: flex;
  gap: 12px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

/* Error and Success Messages */
.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 6px;
  padding: 12px;
  color: #ef4444;
  font-size: 14px;
  margin-bottom: 16px;
}

.success-message {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 6px;
  padding: 12px;
  color: #10b981;
  font-size: 14px;
  margin-bottom: 16px;
}

/* Profile Picture Styles */
.profile-picture-section h2 {
  margin-bottom: 20px;
}

.profile-picture-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.profile-picture {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid #3a3a3a;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-initials {
  width: 100%;
  height: 100%;
  background: #fcd469;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  font-weight: 600;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #3a3a3a;
  border-top: 2px solid #fcd469;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.profile-picture-actions {
  display: flex;
  gap: 12px;
}

.profile-picture-hint {
  text-align: center;
  color: #888888;
  font-size: 14px;
  margin-top: 16px;
  max-width: 400px;
}

/* Password Change Styles */
.password-change-section .password-info {
  color: #b0b0b0;
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}

.security-tips {
  margin-top: 20px;
  padding: 20px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
}

.security-tips h4 {
  color: #fcd469;
  margin: 0 0 12px 0;
  font-size: 16px;
}

.security-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #b0b0b0;
}

.security-tips li {
  margin-bottom: 8px;
  font-size: 14px;
}







/* Subscription Section Styles */
.subscription-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
}

.subscription-description {
  color: #b0b0b0;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.pricing-billing-btn {
  background: #fcd469;
  color: #1a1a1a;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  max-width: 200px;
}

.pricing-billing-btn:hover {
  background: #f5c842;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(252, 212, 105, 0.3);
}

.pricing-billing-btn svg {
  flex-shrink: 0;
}

/* Logout Section Styles */
.logout-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
}

.logout-description {
  color: #b0b0b0;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.logout-btn {
  background: #ef4444;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  max-width: 200px;
}

.logout-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.logout-btn svg {
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    padding: 15px;
  }

  .profile-section {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .edit-actions, .form-actions {
    flex-direction: column;
  }



  .profile-picture-actions {
    flex-direction: column;
    width: 100%;
  }

  .profile-header {
    padding-top: 50px;
  }

  .chat-btn {
    position: static;
    margin-bottom: 20px;
    align-self: flex-start;
  }

  .quick-actions-buttons {
    flex-direction: column;
  }

  .pricing-billing-btn,
  .support-btn,
  .logout-btn {
    max-width: 100%;
  }
}

/* Quick Actions Section Styles */
.quick-actions-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quick-actions-description {
  color: #b0b0b0;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.quick-actions-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.chat-btn {
  position: absolute;
  left: 0;
  top: 0;
  background: transparent;
  border: 1px solid #3a3a3a;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.chat-btn:hover {
  background: #2a2a2a;
  border-color: #fcd469;
  color: #fcd469;
}

.chat-btn svg {
  flex-shrink: 0;
}

/* Support Section Styles */
.support-section {

  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
}

.support-description {
  color: #b0b0b0;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.support-btn {
  background: #10b981;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  max-width: 200px;
}

.support-btn:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.support-btn svg {
  flex-shrink: 0;
}
