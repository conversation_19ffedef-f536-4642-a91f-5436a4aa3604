.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
  background: transparent;
}

.header--scrolled {
  background-color: rgba(10, 10, 10, 0.98);
  border-bottom-color: var(--border-color);
  box-shadow: var(--shadow-light);
}

.header__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  min-height: 70px;
}

.header__logo {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.header__logo:hover {
  opacity: 0.8;
}

.header__logo-img {
  height: 45px;
  width: auto;
  object-fit: contain;
}

.header__nav {
  display: flex;
  align-items: center;
}

.header__nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.header__nav-link {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
  padding: 0.5rem 0;
}

.header__nav-link:hover {
  color: var(--accent-color);
}

.header__actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header__mobile-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  gap: 4px;
}

.header__mobile-toggle span {
  width: 24px;
  height: 2px;
  background-color: var(--text-primary);
  transition: all 0.3s ease;
}

.header__mobile-nav {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--secondary-bg);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-medium);
}

.header__mobile-nav--open {
  display: block;
}

.header__mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 1rem 0;
}

.header__mobile-nav-link {
  display: block;
  width: 100%;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  padding: 1rem 1.5rem;
  text-align: left;
  transition: all 0.3s ease;
}

.header__mobile-nav-link:hover {
  color: var(--accent-color);
  background-color: rgba(252, 212, 105, 0.1);
}

.header__mobile-actions {
  padding: 1rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .header__nav,
  .header__actions {
    display: none;
  }

  .header__mobile-toggle {
    display: flex;
  }

  .header__content {
    padding: 0.75rem 0;
  }

  .header__logo h2 {
    font-size: 1.25rem;
  }
  .header__logo-img{
    height: 35px;
  }
}

/* Tablet Styles */
@media (max-width: 1024px) {
  .header__nav-list {
    gap: 1.5rem;
  }

  .header__actions {
    gap: 0.75rem;
  }
}
