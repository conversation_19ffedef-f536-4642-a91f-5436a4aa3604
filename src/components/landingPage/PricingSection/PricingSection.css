.pricing-section {
  padding: 80px 0;
  background: linear-gradient(180deg, var(--secondary-bg) 0%, var(--primary-bg) 100%);
  position: relative;
}

.pricing-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(252, 212, 105, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(102, 126, 234, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.pricing-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.pricing-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.pricing-header p {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.pricing-error {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.pricing-error p {
  color: var(--text-secondary);
  margin-bottom: 12px;
  font-size: 1rem;
}

.pricing-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.pricing-card {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 32px 24px;
  box-shadow: var(--shadow-medium);
  border: 2px solid var(--border-color);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pricing-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-heavy);
  border-color: var(--accent-color);
}

.pricing-card--popular {
  border-color: var(--accent-color);
  transform: scale(1.05);
}

.pricing-card--popular:hover {
  transform: scale(1.05) translateY(-4px);
}

.pricing-badge {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
  color: var(--primary-bg);
  padding: 8px 24px;
  border-radius: 0 0 12px 12px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pricing-card-header {
  text-align: center;
  margin-bottom: 32px;
  padding-top: 20px;
}

.pricing-card--popular .pricing-card-header {
  padding-top: 40px;
}

.pricing-card-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.pricing-price {
  margin-bottom: 12px;
}

.price-amount {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--text-primary);
}

.pricing-description {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
}

.pricing-features {
  margin-bottom: 32px;
}

.pricing-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pricing-features li {
  display: flex;
  align-items: center;
  padding: 8px 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.feature-icon {
  color: var(--accent-color);
  font-weight: bold;
  margin-right: 12px;
  font-size: 1.1rem;
}

.pricing-card-footer {
  text-align: center;
}

.btn-full {
  width: 100%;
  padding: 14px 24px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.pricing-footer {
  text-align: center;
  margin-top: 60px;
  position: relative;
  z-index: 2;
}

.pricing-footer p {
  color: var(--text-secondary);
  font-size: 1rem;
}

.link-button {
  background: none;
  border: none;
  color: var(--accent-color);
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
  padding: 0;
  transition: color 0.3s ease;
}

.link-button:hover {
  color: var(--accent-hover);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .pricing-section {
    padding: 60px 0;
  }

  .pricing-header h2 {
    font-size: 2rem;
  }

  .pricing-header p {
    font-size: 1.1rem;
  }

  .pricing-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 20px;
  }

  .pricing-card--popular {
    transform: none;
  }

  .pricing-card--popular:hover {
    transform: translateY(-4px);
  }

  .price-amount {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .pricing-card {
    padding: 24px 16px;
  }

  .pricing-header {
    margin-bottom: 40px;
  }

  .pricing-footer {
    margin-top: 40px;
  }
}
