import React from 'react';
import './FeaturesSection.css';

const FeaturesSection = () => {

  const handleStartTrial = () => {
    window.open('/chat', '_blank');
  };

  const features = [
    {
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M12 2L2 7l10 5 10-5-10-5z"/>
          <path d="M2 17l10 5 10-5"/>
          <path d="M2 12l10 5 10-5"/>
        </svg>
      ),
      title: 'All AI Models in One Place',
      description: 'Access OpenAI, Claude, and other leading AI models with a single subscription. Switch between different AI capabilities seamlessly without multiple accounts.',
      highlights: ['OpenAI', 'Claude AI', 'Instant Model Switching', 'Single Subscription']
    },
    {
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
        </svg>
      ),
      title: 'Lightning Fast Responses',
      description: 'Experience near-instantaneous AI responses with our optimized infrastructure and real-time streaming.',
      highlights: ['Real-time Streaming', 'Optimized Performance', 'Global Infrastructure', 'Instant Responses']
    },
    {
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
          <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
        </svg>
      ),
      title: 'Secure & Private',
      description: 'Your conversations are protected with enterprise-grade security. We prioritize your privacy and data protection.',
      highlights: ['End-to-End Encryption', 'GDPR Compliant', 'No Data Mining', 'Secure Storage']
    },
    {
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
        </svg>
      ),
      title: 'Smart Conversations',
      description: 'Maintain context across long conversations with intelligent memory management and organized chat threads.',
      highlights: ['Context Awareness', 'Conversation History', 'Thread Organization', 'Memory Management']
    },
    {
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
          <circle cx="12" cy="7" r="4"/>
        </svg>
      ),
      title: 'Project-Based Organization',
      description: 'Organize your conversations into projects and manage multiple chat threads efficiently with our advanced workspace features.',
      highlights: ['Project Workspaces', 'Thread Management', 'File Attachments', 'Usage Analytics']
    },
    {
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <rect x="2" y="3" width="20" height="14" rx="2" ry="2"/>
          <line x1="8" y1="21" x2="16" y2="21"/>
          <line x1="12" y1="17" x2="12" y2="21"/>
        </svg>
      ),
      title: 'Multi-Platform Access',
      description: 'Access your AI assistant from anywhere with our responsive web application and seamless cross-device synchronization.',
      highlights: ['Web Application', 'Mobile Responsive', 'Cross-Device Sync', 'Cloud Storage']
    }
  ];

  return (
    <section id="features" className="features">
      <div className="container">
        <div className="features__header">
          <h2 className="features__title">
            Why Choose
            <span className="features__title-highlight"> the infini ai</span>
          </h2>
          <p className="features__subtitle">
            Get access to all leading AI models with one subscription. Experience the power of
            OpenAI, Claude, and more in a unified, professional platform designed for productivity.
          </p>
        </div>

        <div className="features__grid">
          {features.map((feature, index) => (
            <div key={index} className="features__card">
              <div className="features__card-header">
                <div className="features__icon">
                  {feature.icon}
                </div>
                <h3 className="features__card-title">{feature.title}</h3>
              </div>

              <p className="features__card-description">
                {feature.description}
              </p>

              <ul className="features__highlights">
                {feature.highlights.map((highlight, highlightIndex) => (
                  <li key={highlightIndex} className="features__highlight-item">
                    <span className="features__highlight-icon">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <polyline points="20,6 9,17 4,12"/>
                      </svg>
                    </span>
                    {highlight}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="features__cta">
          <div className="features__cta-content">
            <h3 className="features__cta-title">Ready to Experience the Future?</h3>
            <p className="features__cta-description">
              Join thousands of users who are already leveraging the power of advanced AI conversations.
            </p>
            <button className="btn btn-primary btn-large" onClick={handleStartTrial}>
              Start Now
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
