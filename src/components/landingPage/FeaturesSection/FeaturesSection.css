.features {
  padding: 5rem 0;
  background: linear-gradient(180deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
  position: relative;
}

.features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(252, 212, 105, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(102, 126, 234, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.features__header {
  text-align: center;
  margin-bottom: 4rem;
  position: relative;
  z-index: 2;
}

.features__title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
  line-height: 1.2;
}

.features__title-highlight {
  background: linear-gradient(135deg, var(--accent-color) 0%, #f5c842 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.features__subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.features__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
  position: relative;
  z-index: 2;
}

.features__card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.features__card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(252, 212, 105, 0.05) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.features__card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-heavy);
  border-color: rgba(252, 212, 105, 0.3);
}

.features__card:hover::before {
  opacity: 1;
}

.features__card-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.features__icon {
  width: 60px;
  height: 60px;
  background: transparent;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  flex-shrink: 0;
  border: 1px solid rgba(252, 212, 105, 0.3);;
}

.features__card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.features__card-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.features__highlights {
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  z-index: 2;
}

.features__highlight-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.features__highlight-item:last-child {
  margin-bottom: 0;
}

.features__highlight-icon {
  width: 16px;
  height: 16px;
  background: var(--accent-color);
  color: #000000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  flex-shrink: 0;
}

.features__cta {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.features__cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(252, 212, 105, 0.1) 0%, rgba(102, 126, 234, 0.05) 100%);
}

.features__cta-content {
  position: relative;
  z-index: 2;
}

.features__cta-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.features__cta-description {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .features {
    padding: 3rem 0;
  }

  .features__header {
    margin-bottom: 3rem;
  }

  .features__title {
    font-size: 2rem;
  }

  .features__subtitle {
    font-size: 1rem;
  }

  .features__grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 3rem;
  }

  .features__card {
    padding: 1.5rem;
  }

  .features__card-header {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }

  .features__icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .features__cta {
    padding: 2rem 1.5rem;
  }

  .features__cta-title {
    font-size: 1.5rem;
  }

  .features__cta-description {
    font-size: 1rem;
  }
}

/* Tablet Styles */
@media (max-width: 1024px) {
  .features__grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .features__title {
    font-size: 2.25rem;
  }
}
