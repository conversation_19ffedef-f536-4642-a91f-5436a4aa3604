import React from 'react';
import './AboutSection.css';

const AboutSection = () => {

  const handleStartTrial = () => {
    window.open('/chat', '_blank');
  };

  const stats = [
    { number: '10K+', label: 'Active Users', description: 'Growing community of AI enthusiasts' },
    { number: '1M+', label: 'Messages Processed', description: 'Conversations powered by AI' },
    { number: '5+', label: 'AI Models', description: 'Access to leading AI technologies' },
    { number: '24/7', label: 'Support', description: 'Always here when you need us' }
  ];



  return (
    <section id="about" className="about">
      <div className="container">
        <div className="about__content">
          <div className="about__text">
            <h2 className="about__title">
              About
              <span className="about__title-highlight"> the infini ai</span>
            </h2>

            <p className="about__description">
              the infini ai revolutionizes how you interact with AI by bringing all leading models under one roof.
              Instead of managing multiple subscriptions and switching between platforms, get instant access to
              OpenAI, <PERSON>, and other state-of-the-art AI models with a single subscription.
            </p>

            <p className="about__description">
              We believe in democratizing access to advanced AI capabilities while maintaining
              the highest standards of security, privacy, and user experience. Our mission is to
              make powerful AI tools accessible to everyone, from individuals to enterprises.
            </p>

            <div className="about__features">
              <div className="about__feature">
                <h4>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{marginRight: '8px', verticalAlign: 'middle'}}>
                    <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
                  </svg>
                  Innovation First
                </h4>
                <p>Constantly evolving with the latest AI advancements and user feedback.</p>
              </div>
              <div className="about__feature">
                <h4>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{marginRight: '8px', verticalAlign: 'middle'}}>
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                  </svg>
                  Privacy Focused
                </h4>
                <p>Your data is protected with enterprise-grade security and encryption.</p>
              </div>
              <div className="about__feature">
                <h4>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{marginRight: '8px', verticalAlign: 'middle'}}>
                    <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26"/>
                  </svg>
                  User Centric
                </h4>
                <p>Designed with user experience and accessibility at the forefront.</p>
              </div>
            </div>
          </div>

          <div className="about__visual">
            <div className="about__stats-grid">
              {stats.map((stat, index) => (
                <div key={index} className="about__stat-card">
                  <div className="about__stat-number">{stat.number}</div>
                  <div className="about__stat-label">{stat.label}</div>
                  <div className="about__stat-description">{stat.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>



        <div className="about__mission">
          <div className="about__mission-content">
            <h3 className="about__mission-title">Our Mission</h3>
            <p className="about__mission-text">
              To bridge the gap between human creativity and artificial intelligence,
              creating a platform where technology enhances human potential rather than replacing it.
              We envision a future where AI conversations are as natural and meaningful as
              human interactions, empowering users to achieve more than they ever thought possible.
            </p>
            <div className="about__mission-values">
              <div className="about__value">
                <span className="about__value-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="9" cy="21" r="1"/>
                    <circle cx="20" cy="21" r="1"/>
                    <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
                  </svg>
                </span>
                <span>Innovation</span>
              </div>
              <div className="about__value">
                <span className="about__value-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                    <circle cx="9" cy="7" r="4"/>
                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                  </svg>
                </span>
                <span>Collaboration</span>
              </div>
              <div className="about__value">
                <span className="about__value-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="3"/>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                  </svg>
                </span>
                <span>Excellence</span>
              </div>
              <div className="about__value">
                <span className="about__value-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M2 12h20"/>
                    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
                  </svg>
                </span>
                <span>Accessibility</span>
              </div>
            </div>

            <div className="about__cta">
              <button className="btn btn-primary btn-large" onClick={handleStartTrial}>
                Start Now
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
