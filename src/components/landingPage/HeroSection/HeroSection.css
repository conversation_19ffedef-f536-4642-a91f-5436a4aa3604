.hero {
  position: relative;
  min-height: 85vh;
  display: flex;
  align-items: center;
  padding-top: 70px;
  overflow: hidden;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s ease-out;
}

.hero--visible {
  opacity: 1;
  transform: translateY(0);
}

.hero__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero__gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse at center, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.08) 50%, transparent 70%),
    linear-gradient(135deg, rgba(252, 212, 105, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);
  animation: gradientShift 8s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background:
      radial-gradient(ellipse at center, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.08) 50%, transparent 70%),
      linear-gradient(135deg, rgba(252, 212, 105, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);
  }
  50% {
    background:
      radial-gradient(ellipse at center, rgba(118, 75, 162, 0.15) 0%, rgba(102, 126, 234, 0.08) 50%, transparent 70%),
      linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(252, 212, 105, 0.05) 100%);
  }
}

.hero__particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  --mouse-x: 50%;
  --mouse-y: 50%;
}

.hero__particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  animation: floatParticle 15s ease-in-out infinite;
}

.hero__particle--1 {
  width: 4px;
  height: 4px;
  background: rgba(252, 212, 105, 0.6);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 20s;
}

.hero__particle--2 {
  width: 6px;
  height: 6px;
  background: rgba(102, 126, 234, 0.4);
  top: 60%;
  left: 80%;
  animation-delay: -5s;
  animation-duration: 25s;
}

.hero__particle--3 {
  width: 3px;
  height: 3px;
  background: rgba(118, 75, 162, 0.5);
  top: 80%;
  left: 20%;
  animation-delay: -10s;
  animation-duration: 18s;
}

.hero__particle--4 {
  width: 5px;
  height: 5px;
  background: rgba(252, 212, 105, 0.3);
  top: 30%;
  left: 70%;
  animation-delay: -15s;
  animation-duration: 22s;
}

.hero__particle--5 {
  width: 2px;
  height: 2px;
  background: rgba(102, 126, 234, 0.7);
  top: 50%;
  left: 30%;
  animation-delay: -8s;
  animation-duration: 16s;
}

.hero__particle--6 {
  width: 4px;
  height: 4px;
  background: rgba(118, 75, 162, 0.4);
  top: 70%;
  left: 60%;
  animation-delay: -12s;
  animation-duration: 24s;
}

@keyframes floatParticle {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-30px) translateX(20px) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-60px) translateX(-10px) scale(0.8);
    opacity: 0.5;
  }
  75% {
    transform: translateY(-20px) translateX(30px) scale(1.1);
    opacity: 0.7;
  }
}

.hero__geometric-shapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.hero__shape {
  position: absolute;
  opacity: 0.1;
  animation: rotateShape 30s linear infinite;
}

.hero__shape--circle {
  width: 100px;
  height: 100px;
  border: 2px solid var(--accent-color);
  border-radius: 50%;
  top: 15%;
  right: 15%;
  animation-delay: 0s;
}

.hero__shape--triangle {
  width: 0;
  height: 0;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-bottom: 52px solid rgba(102, 126, 234, 0.3);
  top: 70%;
  left: 10%;
  animation-delay: -10s;
  animation-duration: 25s;
}

.hero__shape--square {
  width: 60px;
  height: 60px;
  border: 2px solid rgba(118, 75, 162, 0.4);
  top: 40%;
  right: 25%;
  animation-delay: -20s;
  animation-duration: 35s;
}

@keyframes rotateShape {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

.hero__content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  text-align: center;
}

.hero__text {
  z-index: 2;
  max-width: 800px;
}

.hero__title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  min-height: 4.2rem;
}

.hero__title-typed {
  display: inline-block;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 0.5s forwards;
}

.hero__cursor {
  display: inline-block;
  background: var(--accent-color);
  width: 3px;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero__title-highlight {
  background: linear-gradient(135deg, var(--accent-color) 0%, #f5c842 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
  opacity: 0;
  transform: translateY(20px);
  animation: highlightReveal 0.6s ease-out 0s forwards;
}

.hero__title-highlight--animated {
  background-size: 200% 100%;
  animation:
    highlightReveal 0.6s ease-out 0s forwards,
    shimmer 3s ease-in-out 0.8s infinite;
}

@keyframes highlightReveal {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes shimmer {
  0%, 100% { background-position: 200% 0; }
  50% { background-position: -200% 0; }
}

.hero__subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
  opacity: 0;
  transform: translateY(30px);
}

.hero__subtitle--animated {
  animation: fadeInUp 0.8s ease-out 0.8s forwards;
}

.hero__features-list {
  display: flex;
  gap: 2rem;
  margin-bottom: 2.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.hero__features-list--animated .hero__feature-item {
  opacity: 0;
  transform: translateY(30px);
}

.hero__feature-item--1 {
  animation: fadeInUp 0.6s ease-out 1s forwards;
}

.hero__feature-item--2 {
  animation: fadeInUp 0.6s ease-out 1.2s forwards;
}

.hero__feature-item--3 {
  animation: fadeInUp 0.6s ease-out 1.4s forwards;
}

.hero__feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.hero__feature-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(252, 212, 105, 0.3);
  box-shadow: 0 10px 30px rgba(252, 212, 105, 0.1);
}

.hero__feature-icon {
  font-size: 1.25rem;
  transition: all 0.3s ease;
  position: relative;
  top: 3px;
}

.hero__feature-item:hover .hero__feature-icon {
  color: var(--accent-color);
  transform: scale(1.1);
}

.hero__actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  justify-content: center;
  opacity: 0;
  transform: translateY(30px);
}

.hero__actions--animated {
  animation: fadeInUp 0.8s ease-out 1.6s forwards;
}

.hero__cta--enhanced {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--accent-color) 0%, #f5c842 100%);
  border: none;
  transform: perspective(1000px) rotateX(0deg);
  transition: all 0.3s ease;
  box-shadow:
    0 10px 30px rgba(252, 212, 105, 0.3),
    0 0 0 1px rgba(252, 212, 105, 0.1);
}

.hero__cta--enhanced:hover {
  transform: perspective(1000px) rotateX(-5deg) translateY(-5px);
  box-shadow:
    0 20px 40px rgba(252, 212, 105, 0.4),
    0 0 0 1px rgba(252, 212, 105, 0.2);
}

.hero__cta-text {
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.hero__cta-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
  pointer-events: none;
}

.hero__cta--enhanced:active .hero__cta-ripple {
  width: 300px;
  height: 300px;
}

.hero__cta--enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
  z-index: 1;
}

.hero__cta--enhanced:hover::before {
  left: 100%;
}

.hero__learn-more {
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.hero__learn-more:hover {
  border-color: var(--accent-color);
  background: rgba(252, 212, 105, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hero__stats {
  display: flex;
  gap: 3rem;
  flex-wrap: wrap;
  justify-content: center;
}

.hero__stat {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.hero__stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
}

.hero__stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}



/* Mobile Styles */
@media (max-width: 768px) {
  .hero {
    min-height: 90vh;
    padding-top: 80px;
  }

  .hero__title {
    font-size: 2.5rem;
    min-height: 3.2rem;
  }

  .hero__subtitle {
    font-size: 1rem;
  }

  .hero__features-list {
    gap: 1rem;
    flex-direction: column;
    align-items: center;
  }

  .hero__feature-item {
    padding: 0.75rem 1.5rem;
  }

  .hero__actions {
    align-items: center;
    gap: 1.5rem;
    flex-wrap: nowrap;
  }

  .hero__cta--enhanced,
  .hero__learn-more {
    width: 100%;
    max-width: 170px;
  }

  .hero__particle {
    display: none; /* Hide particles on mobile for performance */
  }

  .hero__shape {
    opacity: 0.05; /* Reduce shape opacity on mobile */
  }

  .hero__stats {
    gap: 2rem;
  }
}

/* Tablet Styles */
@media (max-width: 1024px) {
  .hero__title {
    font-size: 2.5rem;
    min-height: 3.8rem;
  }

  .hero__features-list {
    gap: 1.5rem;
  }

  .hero__actions {
    gap: 1.5rem;
  }

  .hero__particle {
    animation-duration: 30s; /* Slower animations on tablet */
  }

  .hero__stats {
    gap: 2rem;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .hero,
  .hero__title-typed,
  .hero__title-highlight,
  .hero__subtitle--animated,
  .hero__feature-item--1,
  .hero__feature-item--2,
  .hero__feature-item--3,
  .hero__actions--animated {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .hero__particle,
  .hero__shape {
    animation: none;
  }

  .hero__cursor {
    animation: none;
    opacity: 1;
  }

  .hero__gradient {
    animation: none;
  }
}
