import React, { useState, useEffect, useRef } from 'react';
import './HeroSection.css';

const HeroSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  const heroRef = useRef(null);
  const particlesRef = useRef(null);



  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (heroRef.current) {
      observer.observe(heroRef.current);
    }

    return () => observer.disconnect();
  }, []);



  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!particlesRef.current) return;

      const rect = particlesRef.current.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;

      particlesRef.current.style.setProperty('--mouse-x', `${x}%`);
      particlesRef.current.style.setProperty('--mouse-y', `${y}%`);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const scrollToFeatures = () => {
    const element = document.getElementById('ctaSection');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleChatNow = () => {
    window.open('/chat', '_blank');
  };

  return (
    <section id="home" className={`hero ${isVisible ? 'hero--visible' : ''}`} ref={heroRef}>
      <div className="hero__background">
        <div className="hero__gradient"></div>
        <div className="hero__particles" ref={particlesRef}>
          <div className="hero__particle hero__particle--1"></div>
          <div className="hero__particle hero__particle--2"></div>
          <div className="hero__particle hero__particle--3"></div>
          <div className="hero__particle hero__particle--4"></div>
          <div className="hero__particle hero__particle--5"></div>
          <div className="hero__particle hero__particle--6"></div>
        </div>
        <div className="hero__geometric-shapes">
          <div className="hero__shape hero__shape--circle"></div>
          <div className="hero__shape hero__shape--triangle"></div>
          <div className="hero__shape hero__shape--square"></div>
        </div>
      </div>

      <div className="container">
        <div className="hero__content">
          <div className="hero__text">
            <h1 className="hero__title">
              <span className="hero__title-typed">
                One Subscription,
                <span className="hero__title-highlight hero__title-highlight--animated">All AI Models</span>
              </span>
            </h1>

            <p className="hero__subtitle hero__subtitle--animated">
              Access OpenAI, Claude, Gemini, and other state-of-the-art AI models in one unified platform.
              Experience the power of multiple AI personalities and capabilities with a single subscription.
            </p>

            <div className="hero__features-list hero__features-list--animated">
              <div className="hero__feature-item hero__feature-item--1">
                <span className="hero__feature-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                    <path d="M2 17l10 5 10-5"/>
                    <path d="M2 12l10 5 10-5"/>
                  </svg>
                </span>
                <span>Multiple AI Models</span>
              </div>
              <div className="hero__feature-item hero__feature-item--2">
                <span className="hero__feature-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26"/>
                  </svg>
                </span>
                <span>Free to start</span>
              </div>
              <div className="hero__feature-item hero__feature-item--3">
                <span className="hero__feature-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                  </svg>
                </span>
                <span>Secure & Private</span>
              </div>
            </div>

            <div className="hero__actions hero__actions--animated">
              <button className="btn btn-primary btn-large hero__cta hero__cta--enhanced" onClick={handleChatNow}>
                <span className="hero__cta-text">Chat Now</span>
                <span className="hero__cta-ripple"></span>
              </button>
              <button
                className="btn btn-secondary btn-large hero__learn-more"
                onClick={scrollToFeatures}
              >
                Learn More
              </button>
            </div>

          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
