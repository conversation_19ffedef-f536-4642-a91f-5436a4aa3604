.footer {
  background: var(--secondary-bg);
  border-top: 1px solid var(--border-color);
  padding: 3rem 0 1rem;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(252, 212, 105, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(102, 126, 234, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.footer__content {
  position: relative;
  z-index: 2;
}

.footer__main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer__brand {
  max-width: 300px;
}

.footer__logo {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.footer__logo-img {
  height: 50px;
  width: auto;
  object-fit: contain;
}

.footer__description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.footer__social {
  display: flex;
  gap: 1rem;
}

.footer__social-link {
  width: 40px;
  height: 40px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 1.25rem;
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 0;
}

.footer__social-link:hover {
  background: var(--accent-color);
  border-color: var(--accent-color);
  transform: translateY(-2px);
}

.footer__links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.footer__link-group {
  display: flex;
  flex-direction: column;
}

.footer__link-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.footer__link-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer__link {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: color 0.3s ease;
  text-align: left;
  padding: 0;
}

.footer__link:hover {
  color: var(--accent-color);
}

.footer__newsletter {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 3rem;
  display: none !important;
}

.footer__newsletter-content {
  max-width: 400px;
  margin: 0 auto;
  text-align: center;
}

.footer__newsletter-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.footer__newsletter-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.footer__newsletter-form {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.footer__newsletter-input {
  flex: 1;
  min-width: 200px;
  padding: 0.75rem 1rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 0.875rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.footer__newsletter-input:focus {
  border-color: var(--accent-color);
}

.footer__newsletter-input::placeholder {
  color: var(--text-muted);
}

.footer__newsletter-btn {
  white-space: nowrap;
}

.footer__bottom {
  border-top: 1px solid var(--border-color);
  padding-top: 2rem;
}

.footer__bottom-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer__copyright {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin: 0;
}

.footer__bottom-links {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.footer__status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.footer__status-dot {
  width: 8px;
  height: 8px;
  background: #28ca42;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 202, 66, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(40, 202, 66, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 202, 66, 0);
  }
}

.footer__back-to-top {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: color 0.3s ease;
  padding: 0;
}

.footer__back-to-top:hover {
  color: var(--accent-color);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .footer {
    padding: 2rem 0 1rem;
  }

  .footer__main {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .footer__brand {
    max-width: none;
  }
  .footer__logo-img{
    height: 40px;
  }

  .footer__social {
    justify-content: center;
  }

  .footer__links {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .footer__newsletter {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .footer__newsletter-form {
    flex-direction: column;
  }

  .footer__newsletter-input {
    min-width: auto;
  }

  .footer__bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .footer__bottom-links {
    justify-content: center;
    gap: 1rem;
  }
}

/* Tablet Styles */
@media (max-width: 1024px) {
  .footer__main {
    gap: 2rem;
  }

  .footer__links {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .footer__newsletter-form {
    flex-direction: column;
  }

  .footer__newsletter-input {
    min-width: auto;
  }
}
