import React from 'react';
import { useNavigate } from 'react-router-dom';
import './Footer.css';

// SVG Icons
const TwitterIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

const LinkedInIcon = () => (
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
    </svg>
);

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const navigate = useNavigate();

  const footerLinks = {
    product: [
      { name: 'Features', href: '#features' },
      { name: 'Pricing', href: '#pricing' },
    ],
    company: [
      { name: 'About Us', href: 'https://blogs.theinfiniai.live/about-the-infini-ai', isRoute: true},
      { name: 'Blog', href: 'https://blogs.theinfiniai.live/', isRoute: true },
      { name: 'Careers', href: '/careers', isRoute: true },
    ],
    support: [
      { name: 'Help Center', href: '/support', isRoute: true },
      { name: 'Support Tickets', href: '/support', isRoute: true },
    ],
    legal: [
      { name: 'Privacy Policy', href: 'https://blogs.theinfiniai.live/privacy-policy', isRoute: true },
      { name: 'Terms of Service', href: 'https://blogs.theinfiniai.live/terms-and-conditions', isRoute: true },
    ]
  };

  const socialLinks = [
    { name: 'Twitter', icon: <TwitterIcon />, href: 'https://x.com/theinfini_ai' },
    { name: 'Instagram', icon: <LinkedInIcon />, href: 'https://www.instagram.com/theinfini_ai/' },
  ];

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId.replace('#', ''));
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleLinkClick = (link) => {
    if (link.isRoute) {
      // Check if it's an external URL
      if (link.href.startsWith('http://') || link.href.startsWith('https://')) {
        window.open(link.href, '_blank', 'noopener,noreferrer');
      } else {
        navigate(link.href);
      }
    } else {
      scrollToSection(link.href);
    }
  };

  const handleSocialClick = (href) => {
    window.open(href, '_blank', 'noopener,noreferrer');
  };

  return (
    <footer id="contact" className="footer">
      <div className="container">
        <div className="footer__content">
          <div className="footer__main">
            <div className="footer__brand">
              <div className="footer__logo">
                <img
                  src="/assets/images/infini-logo.svg"
                  alt="the infini ai"
                  className="footer__logo-img"
                />
              </div>
              <p className="footer__description">
                Empowering conversations with advanced AI technology.
                Experience the future of intelligent interactions today.
              </p>
              <div className="footer__social">
                {socialLinks.map((social, index) => (
                  <button
                    key={index}
                    onClick={() => handleSocialClick(social.href)}
                    className="footer__social-link"
                    aria-label={social.name}
                  >
                    {social.icon}
                  </button>
                ))}
              </div>
            </div>

            <div className="footer__links">
              <div className="footer__link-group">
                <h4 className="footer__link-title">Product</h4>
                <ul className="footer__link-list">
                  {footerLinks.product.map((link, index) => (
                    <li key={index}>
                      <button
                        onClick={() => scrollToSection(link.href)}
                        className="footer__link"
                      >
                        {link.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="footer__link-group">
                <h4 className="footer__link-title">Company</h4>
                <ul className="footer__link-list">
                  {footerLinks.company.map((link, index) => (
                    <li key={index}>
                      <button
                        onClick={() => handleLinkClick(link)}
                        className="footer__link"
                      >
                        {link.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="footer__link-group">
                <h4 className="footer__link-title">Support</h4>
                <ul className="footer__link-list">
                  {footerLinks.support.map((link, index) => (
                    <li key={index}>
                      <button
                        onClick={() => handleLinkClick(link)}
                        className="footer__link"
                      >
                        {link.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="footer__link-group">
                <h4 className="footer__link-title">Legal</h4>
                <ul className="footer__link-list">
                  {footerLinks.legal.map((link, index) => (
                    <li key={index}>
                      <button
                        onClick={() => handleLinkClick(link)}
                        className="footer__link"
                      >
                        {link.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          <div className="footer__newsletter">
            <div className="footer__newsletter-content">
              <h4 className="footer__newsletter-title">Stay Updated</h4>
              <p className="footer__newsletter-description">
                Get the latest updates on new features and AI advancements.
              </p>
              <div className="footer__newsletter-form">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="footer__newsletter-input"
                />
                <button className="btn btn-primary footer__newsletter-btn">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="footer__bottom">
          <div className="footer__bottom-content">
            <p className="footer__copyright">
              © {currentYear} the infini ai. All rights reserved.
            </p>

          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
