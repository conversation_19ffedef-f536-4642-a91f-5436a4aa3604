import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import './CTASection.css';

const CTASection = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  const handleGetStarted = () => {
    if (isAuthenticated) {
      navigate('/chat');
    } else {
      navigate('/signin');
    }
  };

  const scrollToFeatures = () => {
    const element = document.getElementById('features');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="cta" id="ctaSection">
      <div className="cta__background">
        <div className="cta__gradient"></div>
        <div className="cta__particles"></div>
      </div>

      <div className="container">
        <h2 className="cta__title">
          Stop Paying for Multiple
          <span className="cta__title-highlight"> AI Subscriptions</span>
        </h2>
        <div className="cta__content">
          <div className="cta__text">
            <p className="cta__subtitle">
              Get access to OpenAI, Claude, and all leading AI models with one subscription.
              Join thousands of users who've simplified their AI workflow with the infini ai.
            </p>

            <div className="cta__features">
              <div className="cta__feature">
                <span className="cta__feature-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26"/>
                  </svg>
                </span>
                <span>Free to start, no credit card required</span>
              </div>
              <div className="cta__feature">
                <span className="cta__feature-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                    <path d="M2 17l10 5 10-5"/>
                    <path d="M2 12l10 5 10-5"/>
                  </svg>
                </span>
                <span>Instant access to multiple AI models</span>
              </div>
              <div className="cta__feature">
                <span className="cta__feature-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                  </svg>
                </span>
                <span>Enterprise-grade security and privacy</span>
              </div>
            </div>

            <div className="cta__actions">
              <button className="btn btn-primary btn-large cta__primary-btn" onClick={handleGetStarted}>
                {isAuthenticated ? 'Go to Chat' : 'Start Now'}
              </button>
              <button className="btn btn-secondary btn-large" onClick={scrollToFeatures}>
                Learn More
              </button>
            </div>

            <div className="cta__trust-indicators">
              <div className="cta__trust-item">
                <span className="cta__trust-number">10K+</span>
                <span className="cta__trust-label">Happy Users</span>
              </div>
              <div className="cta__trust-item">
                <span className="cta__trust-number">4.9/5</span>
                <span className="cta__trust-label">User Rating</span>
              </div>
              <div className="cta__trust-item">
                <span className="cta__trust-number">24/7</span>
                <span className="cta__trust-label">Support</span>
              </div>
            </div>
          </div>

          <div className="cta__visual">
            <div className="cta__demo-container">
              <div className="cta__demo-header">
                <h3>Try it now!</h3>
                <p>Experience AI conversation instantly</p>
              </div>

              <div className="cta__demo-chat">
                <div className="cta__demo-message cta__demo-message--user">
                  <div className="cta__demo-avatar cta__demo-avatar--user">👤</div>
                  <div className="cta__demo-content">
                    Hello! Can you help me write a creative story?
                  </div>
                </div>

                <div className="cta__demo-message cta__demo-message--ai">
                  <div className="cta__demo-avatar cta__demo-avatar--ai">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <circle cx="12" cy="12" r="3"/>
                      <path d="M12 1v6m0 6v6"/>
                      <path d="m21 12-6-6-6 6-6-6"/>
                    </svg>
                  </div>
                  <div className="cta__demo-content">
                    Absolutely! I'd love to help you create a creative story.
                    What genre or theme interests you? Fantasy, sci-fi, mystery,
                    or something else entirely?
                  </div>
                </div>

                <div className="cta__demo-typing">
                  <div className="cta__demo-avatar cta__demo-avatar--ai">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <circle cx="12" cy="12" r="3"/>
                      <path d="M12 1v6m0 6v6"/>
                      <path d="m21 12-6-6-6 6-6-6"/>
                    </svg>
                  </div>
                  <div className="cta__typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>

              <div className="cta__demo-input">
                <input
                  type="text"
                  placeholder="Type your message here..."
                  className="cta__demo-field"
                  readOnly
                />
                <button className="cta__demo-send">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M22 2L11 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
