import React, { useState } from 'react';
import FileUploadLimitModal from '../common/FileUploadLimitModal';

const FileUploadLimitModalTest = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [testMessage, setTestMessage] = useState('Daily file upload limit reached. You can upload up to 23 files per day. Current: 24');

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  const testWithCustomMessage = () => {
    setTestMessage('Custom test message: File upload limit exceeded!');
    setIsModalOpen(true);
  };

  const testWithDefaultMessage = () => {
    setTestMessage('');
    setIsModalOpen(true);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>🧪 File Upload Limit Modal Test</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Test Controls:</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            onClick={openModal}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#007bff', 
              color: 'white', 
              border: 'none', 
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Open Modal (API Message)
          </button>
          
          <button 
            onClick={testWithCustomMessage}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#28a745', 
              color: 'white', 
              border: 'none', 
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Test Custom Message
          </button>
          
          <button 
            onClick={testWithDefaultMessage}
            style={{ 
              padding: '10px 15px', 
              backgroundColor: '#6c757d', 
              color: 'white', 
              border: 'none', 
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Test Default Message
          </button>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Current Test Message:</h3>
        <div style={{ 
          padding: '10px', 
          backgroundColor: '#f8f9fa', 
          border: '1px solid #dee2e6', 
          borderRadius: '5px',
          fontFamily: 'Arial, sans-serif'
        }}>
          {testMessage || 'Default message will be used'}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Expected Behavior:</h3>
        <ul style={{ lineHeight: '1.6' }}>
          <li>✅ Modal should open when button is clicked</li>
          <li>✅ Modal should display the file upload limit message</li>
          <li>✅ Modal should show "Need more file uploads? Recharge an add-on or upgrade your plan below."</li>
          <li>✅ Modal should have an "Upgrade Plan" button</li>
          <li>✅ Clicking "Upgrade Plan" should navigate to /pricing-billing</li>
          <li>✅ Modal should close when clicking outside or pressing Escape</li>
          <li>✅ Modal should have a file/folder icon (📁)</li>
        </ul>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Integration Notes:</h3>
        <ul style={{ lineHeight: '1.6' }}>
          <li>🔗 Modal is integrated into ChatContext with state management</li>
          <li>🔗 Error detection happens in streamingService.js for 400 status codes</li>
          <li>🔗 File upload functions in ChatContext trigger the modal on file upload limit errors</li>
          <li>🔗 Modal is rendered in ChatLayout.js alongside SignupModal</li>
        </ul>
      </div>

      {/* The actual modal component */}
      <FileUploadLimitModal
        isOpen={isModalOpen}
        onClose={closeModal}
        message={testMessage}
      />
    </div>
  );
};

export default FileUploadLimitModalTest;
