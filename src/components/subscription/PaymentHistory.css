/* Payment History Styles */
.payment-history {
  width: 100%;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #3a3a3a;
  border-top: 4px solid #fcd469;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  margin-top: 16px;
  color: #b0b0b0;
  font-size: 16px;
}

.error-state, .empty-state {
  text-align: center;
  padding: 60px 20px;
  background: #2a2a2a;
  border-radius: 12px;
  border: 1px solid #3a3a3a;
}

.error-state h3, .empty-state h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #ffffff;
}

.error-state p, .empty-state p {
  font-size: 16px;
  color: #b0b0b0;
  margin: 0 0 24px 0;
}

.retry-btn {
  background: #fcd469;
  color: #1a1a1a;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #f5c842;
  transform: translateY(-1px);
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 16px;
  color: #ef4444;
  font-size: 14px;
  margin-bottom: 24px;
  text-align: center;
}

/* Payments List */
.payments-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.payment-item {
  background: #2a2a2a;
  border: 1px solid #3a3a3a;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.payment-item:hover {
  border-color: #fcd469;
  box-shadow: 0 4px 16px rgba(252, 212, 105, 0.1);
}

.payment-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.payment-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.payment-method {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  background: #1a1a1a;
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid #3a3a3a;
}

.payment-status {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.payment-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.payment-amount {
  font-size: 20px;
  font-weight: 700;
  color: #fcd469;
}

.payment-description {
  font-size: 16px;
  color: #ffffff;
  font-weight: 500;
}

.payment-date {
  font-size: 14px;
  color: #888888;
}

.payment-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 140px;
}

.details-btn {
  background: transparent;
  color: #fcd469;
  border: 1px solid #fcd469;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.details-btn:hover {
  background: rgba(252, 212, 105, 0.1);
}

.download-invoice-btn {
  background: transparent;
  color: #10b981;
  border: 1px solid #10b981;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 6px;
}

.download-invoice-btn:hover {
  background: rgba(16, 185, 129, 0.1);
}

.download-invoice-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.download-invoice-btn svg {
  flex-shrink: 0;
}

/* Load More */
.load-more-section {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.load-more-btn {
  background: #fcd469;
  color: #1a1a1a;
  border: none;
  border-radius: 8px;
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.load-more-btn:hover:not(:disabled) {
  background: #f5c842;
  transform: translateY(-1px);
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal {
  background: #2a2a2a;
  border-radius: 16px;
  max-width: 500px;
  width: 100%;
  border: 1px solid #3a3a3a;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  margin-bottom: 20px;
}

.modal-header h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
}

.close-btn {
  background: none;
  border: none;
  color: #888888;
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  transition: color 0.3s ease;
}

.close-btn:hover {
  color: #ffffff;
}

.modal-content {
  padding: 0 24px 24px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
}

.detail-row .label {
  font-size: 14px;
  color: #b0b0b0;
  font-weight: 500;
}

.detail-row .value {
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
  text-align: right;
  word-break: break-all;
}

.detail-row .value.status {
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-main {
    flex-direction: column;
    gap: 16px;
  }

  .payment-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .payment-actions {
    align-self: stretch;
  }

  .details-btn {
    width: 100%;
  }

  .modal {
    margin: 20px;
    max-height: calc(100vh - 40px);
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .detail-row .value {
    text-align: left;
  }
}
