/* Razorpay Checkout Styles */
.razorpay-checkout-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.razorpay-checkout-modal {
  background: #2a2a2a;
  border-radius: 16px;
  max-width: 500px;
  width: 100%;
  border: 1px solid #3a3a3a;
  position: relative;
  max-height: 90vh;
  overflow-y: auto;
}

.checkout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  margin-bottom: 20px;
}

.checkout-header h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
}

.close-btn {
  background: none;
  border: none;
  color: #888888;
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  transition: color 0.3s ease;
  border-radius: 4px;
}

.close-btn:hover:not(:disabled) {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.close-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checkout-content {
  padding: 0 24px 24px 24px;
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 12px 16px;
  color: #ef4444;
  font-size: 14px;
  margin-bottom: 20px;
  text-align: center;
}

/* Payment Summary */
.payment-summary {
  background: #1a1a1a;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #3a3a3a;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.summary-item:last-child {
  margin-bottom: 0;
  padding-top: 12px;
  border-top: 1px solid #3a3a3a;
}

.summary-item .label {
  font-size: 14px;
  color: #b0b0b0;
  font-weight: 500;
}

.summary-item .value {
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
}

.summary-item .value.amount {
  font-size: 18px;
  color: #fcd469;
  font-weight: 700;
}

.summary-item .value.order-id {
  font-size: 12px;
  color: #888888;
  font-family: monospace;
}

/* Payment Info */
.payment-info {
  margin-bottom: 24px;
}

.payment-info h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #ffffff;
}

.payment-info ul {
  margin: 0;
  padding-left: 20px;
  color: #b0b0b0;
}

.payment-info li {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.payment-info li:last-child {
  margin-bottom: 0;
}

/* Checkout Actions */
.checkout-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
}

.cancel-btn {
  background: transparent;
  color: #b0b0b0;
  border: 2px solid #3a3a3a;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover:not(:disabled) {
  border-color: #fcd469;
  color: #fcd469;
}

.cancel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pay-btn {
  background: #fcd469;
  color: #1a1a1a;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.pay-btn:hover:not(:disabled) {
  background: #f5c842;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(252, 212, 105, 0.3);
}

.pay-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(42, 42, 42, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #3a3a3a;
  border-top: 4px solid #fcd469;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .razorpay-checkout-overlay {
    padding: 10px;
  }

  .razorpay-checkout-modal {
    max-height: 95vh;
  }

  .checkout-header {
    padding: 20px 20px 0 20px;
  }

  .checkout-content {
    padding: 0 20px 20px 20px;
  }

  .checkout-actions {
    flex-direction: column;
    gap: 12px;
  }

  .cancel-btn,
  .pay-btn {
    width: 100%;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .summary-item .value.order-id {
    word-break: break-all;
  }
}
