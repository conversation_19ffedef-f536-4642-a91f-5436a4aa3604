/* Subscription Dashboard Styles */
.subscription-dashboard {
  width: 100%;
}

.auth-required, .no-subscription {
  text-align: center;
  padding: 60px 20px;
  background: #2a2a2a;
  border-radius: 12px;
  border: 1px solid #3a3a3a;
}

.auth-required h3, .no-subscription h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #ffffff;
}

.auth-required p, .no-subscription p {
  font-size: 16px;
  color: #b0b0b0;
  margin: 0 0 24px 0;
}

.signin-btn, .view-plans-btn {
  background: #fcd469;
  color: #1a1a1a;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.signin-btn:hover, .view-plans-btn:hover {
  background: #f5c842;
  transform: translateY(-1px);
}

.error-message {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 16px;
  color: #ef4444;
  font-size: 14px;
  margin-bottom: 24px;
  text-align: center;
}

/* Dashboard Sections */
.dashboard-section {
  background: #2a2a2a;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #3a3a3a;
}

.dashboard-section h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  color: #ffffff;
  padding-bottom: 12px;
  border-bottom: 1px solid #3a3a3a;
}

/* Plan Overview */
.plan-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.plan-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.plan-name-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
}

.plan-name {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.plan-status {
  background: rgba(255, 255, 255, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
}

.detail-item .label {
  font-size: 14px;
  color: #b0b0b0;
  font-weight: 500;
}

.detail-item .value {
  font-size: 14px;
  color: #ffffff;
  font-weight: 600;
}

/* Credits Overview */
.credits-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.usage-stat-item {
  background: #1a1a1a;
  border: 1px solid #3a3a3a;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.usage-stat-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.usage-stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #fcd469;
  line-height: 1;
}

.usage-stat-label {
  font-size: 14px;
  color: #b0b0b0;
  margin-top: 4px;
}

.usage-stat-bar {
  width: 100%;
  height: 6px;
  background: #3a3a3a;
  border-radius: 3px;
  overflow: hidden;
}

.usage-stat-fill {
  height: 100%;
  background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Legacy styles for backward compatibility */
.credits-display {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.credits-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.credits-remaining {
  font-size: 32px;
  font-weight: 700;
  color: #fcd469;
  line-height: 1;
}

.credits-label {
  font-size: 14px;
  color: #b0b0b0;
  margin-top: 4px;
}

.credits-bar {
  width: 100%;
  height: 8px;
  background: #3a3a3a;
  border-radius: 4px;
  overflow: hidden;
}

.credits-fill {
  height: 100%;
  background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.credits-reset {
  text-align: center;
  font-size: 14px;
  color: #888888;
  padding: 12px;
  background: #1a1a1a;
  border-radius: 8px;
  border: 1px solid #3a3a3a;
}

/* Usage Statistics */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 20px 16px;
  background: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #3a3a3a;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #fcd469;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 12px;
  color: #b0b0b0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* Status Grid */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #3a3a3a;
}

.status-label {
  font-size: 14px;
  color: #b0b0b0;
  font-weight: 500;
}

.status-value {
  font-size: 16px;
  color: #ffffff;
  font-weight: 600;
}

/* Plan Actions */
.plan-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.cancel-btn {
  background: transparent;
  color: #ef4444;
  border: 2px solid #ef4444;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: rgba(239, 68, 68, 0.1);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal {
  background: #2a2a2a;
  border-radius: 16px;
  padding: 32px;
  max-width: 500px;
  width: 100%;
  border: 1px solid #3a3a3a;
}

.modal h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: #ffffff;
}

.modal p {
  font-size: 16px;
  color: #b0b0b0;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.form-group {
  margin-bottom: 5px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 8px;
}

.form-group textarea {
  width: 100%;
  background: #1a1a1a;
  border: 2px solid #3a3a3a;
  border-radius: 8px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
}

.form-group textarea:focus {
  outline: none;
  border-color: #fcd469;
  box-shadow: 0 0 0 3px rgba(252, 212, 105, 0.1);
}

.modal-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
}

.cancel-modal-btn {
  background: transparent;
  color: #b0b0b0;
  border: 2px solid #3a3a3a;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-modal-btn:hover:not(:disabled) {
  border-color: #fcd469;
  color: #fcd469;
}

.confirm-cancel-btn {
  background: #ef4444;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.confirm-cancel-btn:hover:not(:disabled) {
  background: #dc2626;
}

.cancel-modal-btn:disabled,
.confirm-cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .plan-name-status {
    flex-direction: column;
    align-items: flex-start;
  }

  .plan-details {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .usage-stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .usage-stat-item {
    padding: 12px;
  }

  .usage-stat-value {
    font-size: 20px;
  }

  .modal {
    padding: 24px;
    margin: 20px;
  }

  .modal-actions {
    flex-direction: column;
  }
}
