import apiClient from './apiClient';

class ModelService {
  // Get available LLM models
  async getAvailableModels() {
    try {
      const response = await apiClient.get('/llm-models');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Helper method to format model names for display
  formatModelName(model) {
    // If model is a string (old format), return as is
    if (typeof model === 'string') {
      const modelNames = {
        'gpt-3.5-turbo': 'GPT-3.5 Turbo',
        'gpt-4': 'GPT-4',
        'gpt-4-turbo-preview': 'GPT-4 Turbo',
        'claude-3-haiku-20240307': 'Claude 3 Haiku',
        'claude-3-sonnet-20240229': 'Claude 3 Sonnet',
        'claude-3-opus-20240229': 'Claude 3 Opus'
      };
      return modelNames[model] || model;
    }

    // If model is an object (new format), return displayName
    return model.displayName || model.modelName || model.modelId;
  }

  // Get model provider
  getModelProvider(model) {
    // If model is a string (old format)
    if (typeof model === 'string') {
      if (model.startsWith('gpt-')) {
        return 'OpenAI';
      } else if (model.startsWith('claude-')) {
        return 'Anthropic';
      }
      return 'Unknown';
    }

    // If model is an object (new format), return provider
    return model.provider || 'Unknown';
  }

  // Get model logo URL
  getModelLogoUrl(model) {
    // If model is a string (old format), return null
    if (typeof model === 'string') {
      return null;
    }

    // If model is an object (new format), return logoUrl with proper base URL
    if (model.logoUrl) {
      // If the logoUrl is a relative path, prepend the API base URL
      if (model.logoUrl.startsWith('/')) {
        const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5529';
        return `${baseURL}${model.logoUrl}`;
      }
      // If it's already a full URL, return as is
      return model.logoUrl;
    }

    return null;
  }

  // Get model icon based on provider (fallback for when logo is not available)
  getModelIcon(model) {
    const provider = this.getModelProvider(model);
    switch (provider) {
      case 'OPENAI':
      case 'OpenAI':
        return '🤖';
      case 'ANTHROPIC':
      case 'Anthropic':
        return '🧠';
      case 'GOOGLE':
      case 'Google':
        return '🔍';
      case 'META':
      case 'Meta':
        return '🦙';
      case 'DEEPSEEK':
      case 'DeepSeek':
        return '🔬';
      default:
        return '💬';
    }
  }

  // Get model tier based on pricing
  getModelTier(model) {
    // If model is a string (old format)
    if (typeof model === 'string') {
      const tiers = {
        'gpt-3.5-turbo': 'basic',
        'gpt-4': 'advanced',
        'gpt-4-turbo-preview': 'premium',
        'claude-3-haiku-20240307': 'basic',
        'claude-3-sonnet-20240229': 'advanced',
        'claude-3-opus-20240229': 'premium'
      };
      return tiers[model] || 'basic';
    }

    // If model is an object (new format), determine tier based on pricing
    if (model.pricing && model.pricing.inputTokens) {
      const inputPrice = model.pricing.inputTokens;
      if (inputPrice <= 1) return 'basic';
      if (inputPrice <= 5) return 'advanced';
      return 'premium';
    }

    return 'basic';
  }

  // Get model description
  getModelDescription(model) {
    // If model is a string (old format)
    if (typeof model === 'string') {
      const descriptions = {
        'gpt-3.5-turbo': 'Fast and efficient for most tasks',
        'gpt-4': 'More capable, better reasoning',
        'gpt-4-turbo-preview': 'Latest GPT-4 with improved performance',
        'claude-3-haiku-20240307': 'Quick and responsive',
        'claude-3-sonnet-20240229': 'Balanced performance and capability',
        'claude-3-opus-20240229': 'Most capable Claude model'
      };
      return descriptions[model] || 'AI language model';
    }

    // If model is an object (new format), return description
    return model.description || 'AI language model';
  }

  // Check if model supports multimodal capabilities
  isMultimodal(model) {
    // If model is a string (old format), assume not multimodal
    if (typeof model === 'string') {
      return false;
    }

    // If model is an object (new format), check isMultimodal field
    return model.isMultimodal || false;
  }

  // Get model ID for API calls
  getModelId(model) {
    // If model is a string (old format), return as is
    if (typeof model === 'string') {
      return model;
    }

    // If model is an object (new format), return modelId
    return model.modelId || model.id;
  }

  // Helper method to handle errors
  handleError(error) {
    if (error.response?.data?.message) {
      return new Error(error.response.data.message);
    }
    return new Error(error.message || 'An unexpected error occurred');
  }
}

const modelService = new ModelService();
export default modelService;
