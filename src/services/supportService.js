import apiClient from './apiClient';

class SupportService {
  // Get support information and guidelines
  async getSupportInfo() {
    try {
      const response = await apiClient.get('/support/info');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get available priority options
  async getPriorities() {
    try {
      const response = await apiClient.get('/support/priorities');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get available status options
  async getStatuses() {
    try {
      const response = await apiClient.get('/support/statuses');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Create a new support ticket with optional file attachment
  async createTicket(ticketData, file = null) {
    try {
      // Create FormData for multipart/form-data request
      const formData = new FormData();

      // Add ticket fields
      formData.append('subject', ticketData.subject);
      formData.append('description', ticketData.description);
      formData.append('priority', ticketData.priority || 'MEDIUM');

      if (ticketData.techDetails) {
        formData.append('techDetails', ticketData.techDetails);
      }

      // Add file attachment if provided
      if (file) {
        formData.append('attachment', file);
      }

      // Get auth token
      const token = localStorage.getItem('authToken');

      // Make request with FormData
      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5529/api'}/support/tickets`, {
        method: 'POST',
        headers: {
          ...(token && { Authorization: `Bearer ${token}` })
          // Note: Don't set Content-Type for FormData, browser will set it automatically
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Request failed with status ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get user's support tickets with pagination and filtering
  async getTickets(params = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.page) queryParams.append('page', params.page);
      if (params.limit) queryParams.append('limit', params.limit);
      if (params.status) queryParams.append('status', params.status);
      if (params.priority) queryParams.append('priority', params.priority);

      const url = `/support/tickets${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiClient.get(url);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get a specific ticket by ID
  async getTicketById(ticketId) {
    try {
      const response = await apiClient.get(`/support/tickets/${ticketId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get user's ticket statistics
  async getTicketStats() {
    try {
      const response = await apiClient.get('/support/tickets/stats');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Validate ticket data before submission
  async validateTicket(ticketData) {
    try {
      const response = await apiClient.post('/support/validate', ticketData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Format file size for display
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Get file icon based on MIME type
  getFileIcon(mimeType) {
    if (!mimeType) return '📄';

    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType === 'application/pdf') return '📕';
    if (mimeType.includes('word') || mimeType.includes('document')) return '📝';
    if (mimeType.includes('text')) return '📄';
    if (mimeType.includes('zip') || mimeType.includes('archive')) return '📦';

    return '📄';
  }

  // Get secure file URL for viewing attachments
  getSecureFileUrl(secureFileId) {
    if (!secureFileId) return null;
    return `${process.env.REACT_APP_API_URL || 'http://localhost:5529/api'}/files/${secureFileId}`;
  }

  // Helper method to handle API errors
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;

      switch (status) {
        case 400:
          return new Error(data.message || 'Invalid request data');
        case 401:
          return new Error('Authentication required');
        case 404:
          return new Error('Ticket not found');
        case 429:
          return new Error('Rate limit exceeded. Please try again later.');
        case 500:
          return new Error('Server error. Please try again later.');
        default:
          return new Error(data.message || 'An unexpected error occurred');
      }
    } else if (error.request) {
      // Network error
      return new Error('Network error. Please check your connection.');
    } else {
      // Other error
      return new Error(error.message || 'An unexpected error occurred');
    }
  }

  // Helper method to format priority for display
  formatPriority(priority) {
    const priorityMap = {
      'LOW': { label: 'Low', color: '#10b981' },
      'MEDIUM': { label: 'Medium', color: '#f59e0b' },
      'HIGH': { label: 'High', color: '#ef4444' },
      'URGENT': { label: 'Urgent', color: '#dc2626' }
    };
    return priorityMap[priority] || { label: priority, color: '#6b7280' };
  }

  // Helper method to format status for display
  formatStatus(status) {
    const statusMap = {
      'OPEN': { label: 'Open', color: '#3b82f6' },
      'IN_PROGRESS': { label: 'In Progress', color: '#f59e0b' },
      'RESOLVED': { label: 'Resolved', color: '#10b981' },
      'CLOSED': { label: 'Closed', color: '#6b7280' }
    };
    return statusMap[status] || { label: status, color: '#6b7280' };
  }

  // Helper method to format date for display
  formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Helper method to get relative time
  getRelativeTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return this.formatDate(dateString);
  }
}

const supportService = new SupportService();
export default supportService;
