import sessionManager from '../utils/sessionManager';
import fileUploadService from './fileUploadService';

class WebSearchService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5529/api';
  }

  /**
   * Create streaming request for web search
   */
  async createStreamingRequest(endpoint, data, options = {}) {
    const token = localStorage.getItem('authToken');

    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // Add authentication header for authenticated users
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // Include session ID in request body for conversation continuity
    const requestData = { ...data };

    // Add sessionId for guest users
    const sessionId = data.sessionId || sessionManager.getCurrentSessionId();
    if (sessionId) {
      requestData.sessionId = sessionId;
    }

    const requestOptions = {
      method: 'POST',
      headers,
      body: JSON.stringify(requestData),
      ...options
    };

    return fetch(`${this.baseURL}${endpoint}`, requestOptions);
  }

  /**
   * Parse Server-Sent Events stream
   */
  async parseSSEStream(response, options = {}) {
    const {
      onStart = () => {},
      onChunk = () => {},
      onComplete = () => {},
      onError = () => {},
      signal
    } = options;

    if (!response.ok) {
      const errorText = await response.text();
      let errorMessage;
      try {
        const errorData = JSON.parse(errorText);
        errorMessage = errorData.message || `Request failed with status ${response.status}`;
      } catch {
        errorMessage = `Request failed with status ${response.status}`;
      }
      throw new Error(errorMessage);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullResponse = '';
    let buffer = '';

    try {
      while (true) {
        if (signal?.aborted) {
          throw new Error('Request aborted');
        }

        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          if (!line.startsWith('data: ')) continue;

          const data = line.slice(6);
          if (data === '[DONE]') continue;

          try {
            const parsed = JSON.parse(data);

            if (parsed.type === 'status') {
              onStart(parsed);
            } else if (parsed.type === 'chunk') {
              fullResponse += parsed.content;
              onChunk(parsed.content, fullResponse);
            } else if (parsed.type === 'complete') {
              onComplete(fullResponse, parsed);
              return {
                response: fullResponse,
                metadata: parsed.metadata || {}
              };
            } else if (parsed.type === 'error') {
              throw new Error(parsed.error || 'Web search failed');
            }
          } catch (parseError) {
            console.warn('Failed to parse SSE data:', parseError);
          }
        }
      }
    } catch (error) {
      onError(error);
      throw error;
    } finally {
      reader.releaseLock();
    }

    return {
      response: fullResponse,
      metadata: {}
    };
  }

  /**
   * Stream web search query
   */
  async streamWebSearch(query, options = {}) {
    const { sessionId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const data = {
      query,
      llmModel
    };

    // Include sessionId if provided for conversation continuity
    if (sessionId) {
      data.sessionId = sessionId;
    }

    const response = await this.createStreamingRequest('/websearch/query', data);
    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Stream web search with file attachment
   */
  async streamWebSearchWithFile(query, file, options = {}) {
    const { sessionId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    // Validate file
    const validation = fileUploadService.validateFile(file);
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '));
    }

    const response = await this.createStreamingRequestWithFile(
      '/websearch/query/attachment',
      query,
      file,
      { sessionId, llmModel }
    );

    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Create streaming request with file upload for web search
   */
  async createStreamingRequestWithFile(endpoint, query, file, options = {}) {
    const token = localStorage.getItem('authToken');

    const headers = {
      ...options.headers
      // Don't set Content-Type for FormData - browser will set it with boundary
    };

    // Add authentication header for authenticated users
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const formData = this.createFormDataForWebSearch(query, file, options);

    const requestOptions = {
      method: 'POST',
      headers,
      body: formData,
      ...options
    };

    return fetch(`${this.baseURL}${endpoint}`, requestOptions);
  }

  /**
   * Create FormData for web search with file upload
   */
  createFormDataForWebSearch(query, file, options = {}) {
    const formData = new FormData();
    
    // Add query and other data
    formData.append('query', query);
    
    if (options.sessionId) {
      formData.append('sessionId', options.sessionId);
    }
    
    if (options.llmModel) {
      formData.append('llmModel', options.llmModel);
    }
    
    // Add file
    if (file) {
      formData.append('file', file);
    }

    return formData;
  }

  /**
   * Get web search history
   */
  async getSearchHistory(sessionId = null) {
    const token = localStorage.getItem('authToken');
    
    const headers = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    if (sessionId) {
      headers['Session-ID'] = sessionId;
    }

    const response = await fetch(`${this.baseURL}/websearch/history`, {
      method: 'GET',
      headers
    });

    if (!response.ok) {
      throw new Error(`Failed to get search history: ${response.status}`);
    }

    return response.json();
  }

  /**
   * Check web search service status
   */
  async getServiceStatus() {
    const response = await fetch(`${this.baseURL}/websearch/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get service status: ${response.status}`);
    }

    return response.json();
  }

  /**
   * Handle streaming errors
   */
  handleStreamingError(error) {
    console.error('Web search streaming error:', error);

    // Check for specific error types
    if (error.message?.includes('credits')) {
      return {
        message: 'Insufficient credits for web search. Please upgrade your plan.',
        isSignupRequired: false
      };
    }

    if (error.message?.includes('rate limit')) {
      return {
        message: 'Rate limit exceeded. Please try again in a moment.',
        isSignupRequired: false
      };
    }

    if (error.message?.includes('unauthorized') || error.message?.includes('401')) {
      return {
        message: 'Authentication required. Please sign in to continue.',
        isSignupRequired: true
      };
    }

    // Default error handling
    return {
      message: error.message || 'Web search failed. Please try again.',
      isSignupRequired: false
    };
  }
}

const webSearchService = new WebSearchService();
export default webSearchService;
