import sessionManager from '../utils/sessionManager';
import fileUploadService from './fileUploadService';

class StreamingService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5529/api';
  }

  /**
   * Create simplified streaming request according to SIMPLE_STREAMING_API.md
   * No CSRF tokens, simplified session handling
   */
  async createStreamingRequest(endpoint, data, options = {}) {
    const token = localStorage.getItem('authToken');

    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // Add authentication header for authenticated users
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    // Include session ID in request body for conversation continuity
    const requestData = { ...data };

    // Only add sessionId for guest chat endpoints, not for thread/project endpoints
    const isGuestChatEndpoint = endpoint === '/chat/message/stream';

    if (isGuestChatEndpoint) {
      // Get session ID from session manager or use provided sessionId
      const sessionId = data.sessionId || sessionManager.getCurrentSessionId();
      if (sessionId) {
        requestData.sessionId = sessionId;
      }
    }

    const requestOptions = {
      method: 'POST',
      headers,
      body: JSON.stringify(requestData),
      ...options
    };



    return fetch(`${this.baseURL}${endpoint}`, requestOptions);
  }

  /**
   * Parse Server-Sent Events stream
   */
  async parseSSEStream(response, callbacks = {}) {
    const {
      onStart = () => {},
      onChunk = () => {},
      onComplete = () => {},
      onError = () => {},
      onProgress = () => {},
      onMetadata = () => {}
    } = callbacks;

    if (!response.ok) {
      const errorText = await response.text();

      // Try to parse the error response to check for specific signup message
      let errorData = null;
      try {
        errorData = JSON.parse(errorText);
      } catch (parseError) {
        // If parsing fails, use the raw error text
      }

      // Create a detailed error object for 403 responses
      if (response.status === 403) {

        if (errorData && errorData.message) {
          const isSignupRequired = errorData.message.includes('Please sign up for unlimited access');

          const error = new Error(errorData.message);
          error.status = response.status;
          error.isSignupRequired = isSignupRequired;
          error.originalResponse = errorData;
          throw error;
        } else {
          // Fallback for 403 without proper JSON response
          const error = new Error('Access denied');
          error.status = response.status;
          throw error;
        }
      }

      // Create a detailed error object for 400 responses (file upload limit)
      if (response.status === 400) {

        if (errorData && errorData.message) {
          const isFileUploadLimit = errorData.message.includes('Daily file upload limit reached');

          const error = new Error(errorData.message);
          error.status = response.status;
          error.isFileUploadLimit = isFileUploadLimit;
          error.originalResponse = errorData;
          throw error;
        } else {
          // Fallback for 400 without proper JSON response
          const error = new Error('Bad request');
          error.status = response.status;
          throw error;
        }
      }

      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              switch (data.type) {
                case 'start':
                  onStart(data);
                  if (data.metadata) {
                    onMetadata(data.metadata);
                  }
                  break;

                case 'chunk':
                  fullResponse += data.content;
                  onChunk(data.content, fullResponse);
                  onProgress(fullResponse);
                  break;

                case 'complete':
                  onComplete(data.fullResponse || fullResponse, data);
                  return {
                    response: data.fullResponse || fullResponse,
                    metadata: data.metadata || {}
                  };

                case 'error':
                  onError(new Error(data.error));
                  throw new Error(data.error);

                default:
                  console.warn('Unknown SSE event type:', data.type);
              }
            } catch (parseError) {
              console.error('Failed to parse SSE data:', parseError, 'Line:', line);
            }
          }
        }
      }
    } catch (error) {
      onError(error);
      throw error;
    } finally {
      reader.releaseLock();
    }

    return {
      response: fullResponse,
      metadata: {}
    };
  }

  /**
   * Stream chat message (regular or guest)
   * Uses sessionId for conversation continuity
   */
  async streamChatMessage(message, options = {}) {
    const { sessionId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const data = {
      message,
      llmModel
    };

    // Include sessionId if provided for conversation continuity
    if (sessionId) {
      data.sessionId = sessionId;
    }

    const response = await this.createStreamingRequest('/chat/message/stream', data);
    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Stream thread message
   * Uses threadId for conversation continuity (no sessionId needed)
   */
  async streamThreadMessage(message, options = {}) {
    const { threadId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const data = {
      message,
      llmModel
    };

    // Include threadId for conversation continuity (sessionId is not used for thread messages)
    if (threadId) {
      data.threadId = threadId;
    }

    const response = await this.createStreamingRequest('/threads/message/stream', data);
    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Stream project message
   * Uses threadId for conversation continuity within the project (no sessionId needed)
   */
  async streamProjectMessage(projectId, message, options = {}) {
    const { threadId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const data = {
      message,
      llmModel
    };

    // Include threadId for conversation continuity within the project (sessionId is not used for project messages)
    if (threadId) {
      data.threadId = threadId;
    }

    const response = await this.createStreamingRequest(`/projects/${projectId}/message/stream`, data);
    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Stream regenerate message
   * Uses messageId to regenerate AI response
   */
  async streamRegenerateMessage(messageId, options = {}) {
    const { llmModel = 'gpt-3.5-turbo', attachmentS3Url, ...streamOptions } = options;

    const data = {
      messageId,
      llmModel // Always include llmModel parameter
    };

    // Include attachment URL if provided for context
    if (attachmentS3Url) {
      // Remove the API base URL prefix if present
      let cleanAttachmentUrl = attachmentS3Url;
      const apiPrefix = 'http://localhost:5529/api/files/';
      if (attachmentS3Url.startsWith(apiPrefix)) {
        cleanAttachmentUrl = attachmentS3Url.replace(apiPrefix, '');
      }
      // Send as attachmentUrl instead of attachmentS3Url
      data.attachmentUrl = cleanAttachmentUrl;
    }

    const response = await this.createStreamingRequest('/chat/regenerate', data);
    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Stream chat message with file attachment
   */
  async streamChatMessageWithFile(message, file, options = {}) {
    const { sessionId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const response = await fileUploadService.streamChatMessageWithFile(message, file, {
      sessionId,
      llmModel
    });

    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Stream thread message with file attachment
   */
  async streamThreadMessageWithFile(message, file, options = {}) {
    const { threadId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const response = await fileUploadService.streamThreadMessageWithFile(message, file, {
      threadId,
      llmModel
    });

    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Stream project message with file attachment
   */
  async streamProjectMessageWithFile(projectId, message, file, options = {}) {
    const { threadId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const response = await fileUploadService.streamProjectMessageWithFile(projectId, message, file, {
      threadId,
      llmModel
    });

    return this.parseSSEStream(response, streamOptions);
  }

  /**
   * Handle streaming errors
   */
  handleStreamingError(error) {
    if (error.name === 'AbortError') {
      return new Error('Request was cancelled');
    }

    if (error.message.includes('Failed to fetch')) {
      return new Error('Network error: Please check your connection');
    }

    if (error.message.includes('HTTP 401')) {
      return new Error('Authentication required');
    }

    if (error.message.includes('HTTP 403')) {
      // Check if this is a signup-required error
      if (error.isSignupRequired) {
        const signupError = new Error(error.message);
        signupError.isSignupRequired = true;
        signupError.status = error.status;
        signupError.originalResponse = error.originalResponse;
        return signupError;
      }
      return new Error('Access denied');
    }

    if (error.message.includes('HTTP 400')) {
      // Check if this is a file upload limit error
      if (error.isFileUploadLimit) {
        const fileUploadError = new Error(error.message);
        fileUploadError.isFileUploadLimit = true;
        fileUploadError.status = error.status;
        fileUploadError.originalResponse = error.originalResponse;
        return fileUploadError;
      }
      return new Error('Bad request');
    }

    if (error.message.includes('HTTP 429')) {
      return new Error('Rate limit exceeded. Please wait before sending another message.');
    }

    // Preserve signup-required flag if it exists
    if (error.isSignupRequired) {
      const preservedError = new Error(error.message);
      preservedError.isSignupRequired = true;
      preservedError.status = error.status;
      preservedError.originalResponse = error.originalResponse;
      return preservedError;
    }

    // Preserve file upload limit flag if it exists
    if (error.isFileUploadLimit) {
      const preservedError = new Error(error.message);
      preservedError.isFileUploadLimit = true;
      preservedError.status = error.status;
      preservedError.originalResponse = error.originalResponse;
      return preservedError;
    }

    return error;
  }
}

const streamingService = new StreamingService();
export default streamingService;
